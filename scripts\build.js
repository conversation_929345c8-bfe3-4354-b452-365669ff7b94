#!/usr/bin/env node

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Build configuration
const config = {
  outputDir: 'dist',
  publicPath: '/',
  minify: process.env.NODE_ENV === 'production',
  sourcemap: process.env.NODE_ENV !== 'production',
  analyze: process.argv.includes('--analyze')
};

console.log('🚀 Starting build process...');
console.log('Environment:', process.env.NODE_ENV || 'development');

try {
  // Clean output directory
  console.log('🧹 Cleaning output directory...');
  if (existsSync(join(rootDir, config.outputDir))) {
    execSync(`rm -rf ${config.outputDir}`, { cwd: rootDir });
  }
  mkdirSync(join(rootDir, config.outputDir), { recursive: true });

  // Run Vite build
  console.log('📦 Building application...');
  const buildCommand = `vite build ${config.analyze ? '--analyze' : ''}`;
  execSync(buildCommand, { 
    cwd: rootDir, 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });

  // Generate build info
  console.log('📝 Generating build info...');
  const buildInfo = {
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    commit: getGitCommit(),
    branch: getGitBranch(),
    environment: process.env.NODE_ENV || 'production',
    buildId: generateBuildId()
  };

  writeFileSync(
    join(rootDir, config.outputDir, 'build-info.json'),
    JSON.stringify(buildInfo, null, 2)
  );

  // Copy additional files
  console.log('📋 Copying additional files...');
  copyFile('public/manifest.json', 'manifest.json');
  copyFile('public/sw.js', 'sw.js');

  // Generate service worker with cache busting
  console.log('⚙️ Updating service worker...');
  updateServiceWorker();

  // Generate sitemap (if needed)
  if (process.env.GENERATE_SITEMAP === 'true') {
    console.log('🗺️ Generating sitemap...');
    generateSitemap();
  }

  // Analyze bundle size
  if (config.analyze) {
    console.log('📊 Analyzing bundle size...');
    analyzeBundleSize();
  }

  console.log('✅ Build completed successfully!');
  console.log(`📁 Output directory: ${config.outputDir}`);
  console.log(`🆔 Build ID: ${buildInfo.buildId}`);

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Helper functions
function getGitCommit() {
  try {
    return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return 'unknown';
  }
}

function getGitBranch() {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return 'unknown';
  }
}

function generateBuildId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function copyFile(src, dest) {
  const srcPath = join(rootDir, src);
  const destPath = join(rootDir, config.outputDir, dest);
  
  if (existsSync(srcPath)) {
    const content = readFileSync(srcPath);
    writeFileSync(destPath, content);
    console.log(`  ✓ Copied ${src} → ${dest}`);
  }
}

function updateServiceWorker() {
  const swPath = join(rootDir, config.outputDir, 'sw.js');
  
  if (existsSync(swPath)) {
    let content = readFileSync(swPath, 'utf8');
    
    // Update cache version with build timestamp
    const cacheVersion = `petatalenta-v${Date.now()}`;
    content = content.replace(/const CACHE_NAME = '[^']*'/, `const CACHE_NAME = '${cacheVersion}'`);
    content = content.replace(/const STATIC_CACHE = '[^']*'/, `const STATIC_CACHE = '${cacheVersion}-static'`);
    content = content.replace(/const DYNAMIC_CACHE = '[^']*'/, `const DYNAMIC_CACHE = '${cacheVersion}-dynamic'`);
    
    writeFileSync(swPath, content);
    console.log('  ✓ Updated service worker cache version');
  }
}

function generateSitemap() {
  const routes = [
    '/',
    '/login',
    '/register',
    '/dashboard',
    '/profile'
  ];

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${routes.map(route => `  <url>
    <loc>${process.env.SITE_URL || 'https://petatalenta.com'}${route}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${route === '/' ? '1.0' : '0.8'}</priority>
  </url>`).join('\n')}
</urlset>`;

  writeFileSync(join(rootDir, config.outputDir, 'sitemap.xml'), sitemap);
  console.log('  ✓ Generated sitemap.xml');
}

function analyzeBundleSize() {
  try {
    // This would integrate with bundle analyzer tools
    console.log('  📊 Bundle analysis would be shown here');
    console.log('  💡 Consider using tools like webpack-bundle-analyzer or rollup-plugin-analyzer');
  } catch (error) {
    console.warn('  ⚠️ Bundle analysis failed:', error.message);
  }
}
