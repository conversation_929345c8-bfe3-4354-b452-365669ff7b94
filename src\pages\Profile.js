import { AuthService } from '../services/authService.js';
import { AuthManager, Validator, Router } from '../utils/auth.js';
import { LoadingSpinner } from '../components/common/LoadingSpinner.js';
import { ErrorMessage } from '../components/common/ErrorMessage.js';
import { Modal } from '../components/common/Modal.js';
import { Toast } from '../components/common/Toast.js';

export class Profile {
  constructor(container) {
    this.container = container;
    this.user = null;
    this.profile = null;
    this.init();
  }

  async init() {
    try {
      this.renderLoading();
      await this.fetchUserProfile();
      this.render();
      this.attachEventListeners();
    } catch (error) {
      this.renderError(error.message);
    }
  }

  async fetchUserProfile() {
    try {
      const response = await AuthService.getProfile();
      if (response.success) {
        this.user = response.data.user;
        this.profile = response.data.profile || {};
      } else {
        throw new Error('Failed to fetch profile data');
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  }

  renderLoading() {
    this.container.innerHTML = LoadingSpinner.renderFullPage('Loading profile...');
  }

  renderError(message) {
    this.container.innerHTML = ErrorMessage.renderFullPage(message || 'Failed to load profile');
  }

  render() {
    this.container.innerHTML = `
      <div class="min-h-screen bg-gray-50 py-12">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="bg-white shadow rounded-lg overflow-hidden">
            <!-- Profile Header -->
            <div class="bg-blue-600 px-6 py-8">
              <div class="flex items-center">
                <div class="bg-white rounded-full p-2">
                  <svg class="h-16 w-16 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path>
                  </svg>
                </div>
                <div class="ml-6">
                  <h1 class="text-2xl font-bold text-white">
                    ${this.profile?.full_name || 'User Profile'}
                  </h1>
                  <p class="text-blue-100">
                    ${this.user?.email || ''}
                  </p>
                </div>
              </div>
            </div>

            <!-- Profile Form -->
            <div class="p-6">
              <form id="profile-form" class="space-y-6">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                    <input 
                      type="text" 
                      id="username" 
                      name="username" 
                      value="${this.user?.username || ''}" 
                      class="mt-1 input-field"
                    >
                    <div id="username-error" class="error-message hidden"></div>
                  </div>

                  <div>
                    <label for="full_name" class="block text-sm font-medium text-gray-700">Full Name</label>
                    <input 
                      type="text" 
                      id="full_name" 
                      name="full_name" 
                      value="${this.profile?.full_name || ''}" 
                      class="mt-1 input-field"
                    >
                    <div id="full_name-error" class="error-message hidden"></div>
                  </div>

                  <div>
                    <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth</label>
                    <input 
                      type="date" 
                      id="date_of_birth" 
                      name="date_of_birth" 
                      value="${this.profile?.date_of_birth || ''}" 
                      class="mt-1 input-field"
                    >
                    <div id="date_of_birth-error" class="error-message hidden"></div>
                  </div>

                  <div>
                    <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
                    <select id="gender" name="gender" class="mt-1 input-field">
                      <option value="" ${!this.profile?.gender ? 'selected' : ''}>Select Gender</option>
                      <option value="male" ${this.profile?.gender === 'male' ? 'selected' : ''}>Male</option>
                      <option value="female" ${this.profile?.gender === 'female' ? 'selected' : ''}>Female</option>
                    </select>
                    <div id="gender-error" class="error-message hidden"></div>
                  </div>

                  <div>
                    <label for="school_id" class="block text-sm font-medium text-gray-700">School ID</label>
                    <input 
                      type="number" 
                      id="school_id" 
                      name="school_id" 
                      value="${this.profile?.school_id || ''}" 
                      class="mt-1 input-field"
                    >
                    <div id="school_id-error" class="error-message hidden"></div>
                  </div>
                </div>

                <div id="form-error" class="error-message hidden text-center"></div>
                <div id="form-success" class="success-message hidden text-center"></div>

                <div class="flex justify-end">
                  <button 
                    type="submit" 
                    id="save-profile-btn"
                    class="btn-primary"
                  >
                    <span id="save-text">Save Changes</span>
                    <span id="save-loading" class="hidden">Saving...</span>
                  </button>
                </div>
              </form>

              <!-- Change Password Section -->
              <div class="mt-10 pt-10 border-t border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Change Password</h2>
                <form id="password-form" class="mt-6 space-y-6">
                  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label for="currentPassword" class="block text-sm font-medium text-gray-700">Current Password</label>
                      <input 
                        type="password" 
                        id="currentPassword" 
                        name="currentPassword" 
                        class="mt-1 input-field"
                      >
                      <div id="currentPassword-error" class="error-message hidden"></div>
                    </div>

                    <div>
                      <label for="newPassword" class="block text-sm font-medium text-gray-700">New Password</label>
                      <input 
                        type="password" 
                        id="newPassword" 
                        name="newPassword" 
                        class="mt-1 input-field"
                      >
                      <div id="newPassword-error" class="error-message hidden"></div>
                    </div>
                  </div>

                  <div id="password-form-error" class="error-message hidden text-center"></div>
                  <div id="password-form-success" class="success-message hidden text-center"></div>

                  <div class="flex justify-end">
                    <button 
                      type="submit" 
                      id="change-password-btn"
                      class="btn-primary"
                    >
                      <span id="password-save-text">Change Password</span>
                      <span id="password-save-loading" class="hidden">Changing...</span>
                    </button>
                  </div>
                </form>
              </div>

              <!-- Delete Account Section -->
              <div class="mt-10 pt-10 border-t border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Delete Account</h2>
                <p class="mt-1 text-sm text-gray-500">
                  Once you delete your account, there is no going back. Please be certain.
                </p>
                <div class="mt-6">
                  <button 
                    id="delete-account-btn"
                    class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    const profileForm = this.container.querySelector('#profile-form');
    const passwordForm = this.container.querySelector('#password-form');
    const deleteAccountBtn = this.container.querySelector('#delete-account-btn');

    profileForm.addEventListener('submit', this.handleProfileUpdate.bind(this));
    passwordForm.addEventListener('submit', this.handlePasswordChange.bind(this));
    deleteAccountBtn.addEventListener('click', this.handleDeleteAccount.bind(this));
  }

  async handleProfileUpdate(e) {
    e.preventDefault();
    
    const username = this.container.querySelector('#username').value;
    const fullName = this.container.querySelector('#full_name').value;
    const dateOfBirth = this.container.querySelector('#date_of_birth').value;
    const gender = this.container.querySelector('#gender').value;
    const schoolId = this.container.querySelector('#school_id').value;
    
    // Clear previous errors
    this.clearErrors('form');
    
    // Validate form
    if (!this.validateProfileForm(username, fullName, dateOfBirth, gender, schoolId)) {
      return;
    }

    // Show loading state
    this.setLoading('save', true);

    try {
      const profileData = {
        username: username || undefined,
        full_name: fullName || undefined,
        date_of_birth: dateOfBirth || undefined,
        gender: gender || undefined,
        school_id: schoolId ? parseInt(schoolId) : undefined
      };

      await AuthService.updateProfile(profileData);
      Toast.success('Profile updated successfully!');

      // Refresh profile data
      await this.fetchUserProfile();
      this.render();
      this.attachEventListeners();
    } catch (error) {
      this.showError('form', error.message);
    } finally {
      this.setLoading('save', false);
    }
  }

  async handlePasswordChange(e) {
    e.preventDefault();
    
    const currentPassword = this.container.querySelector('#currentPassword').value;
    const newPassword = this.container.querySelector('#newPassword').value;
    
    // Clear previous errors
    this.clearErrors('password-form');
    
    // Validate form
    if (!this.validatePasswordForm(currentPassword, newPassword)) {
      return;
    }

    // Show loading state
    this.setLoading('password-save', true);

    try {
      await AuthService.changePassword(currentPassword, newPassword);
      Toast.success('Password changed successfully!');

      // Clear password fields
      this.container.querySelector('#currentPassword').value = '';
      this.container.querySelector('#newPassword').value = '';
    } catch (error) {
      this.showError('password-form', error.message);
    } finally {
      this.setLoading('password-save', false);
    }
  }

  async handleDeleteAccount() {
    const confirmed = await Modal.confirm({
      title: 'Delete Account',
      message: 'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.',
      confirmText: 'Delete Account',
      cancelText: 'Cancel'
    });

    if (confirmed) {
      try {
        await AuthService.deleteAccount();
        Toast.success('Account deleted successfully');
        Router.navigate('/login');
      } catch (error) {
        Toast.error(`Failed to delete account: ${error.message}`);
      }
    }
  }

  validateProfileForm(username, fullName, dateOfBirth, gender, schoolId) {
    let isValid = true;

    if (username && !Validator.validateUsername(username)) {
      this.showFieldError('username', 'Username must be 3-100 characters and alphanumeric only');
      isValid = false;
    }

    if (fullName && fullName.length > 100) {
      this.showFieldError('full_name', 'Full name must be at most 100 characters');
      isValid = false;
    }

    if (dateOfBirth) {
      const today = new Date();
      const birthDate = new Date(dateOfBirth);
      if (birthDate > today) {
        this.showFieldError('date_of_birth', 'Date of birth cannot be in the future');
        isValid = false;
      }
    }

    if (gender && !['male', 'female'].includes(gender)) {
      this.showFieldError('gender', 'Gender must be either male or female');
      isValid = false;
    }

    if (schoolId && (!Number.isInteger(Number(schoolId)) || Number(schoolId) <= 0)) {
      this.showFieldError('school_id', 'School ID must be a positive integer');
      isValid = false;
    }

    return isValid;
  }

  validatePasswordForm(currentPassword, newPassword) {
    let isValid = true;

    if (!Validator.validateRequired(currentPassword)) {
      this.showFieldError('currentPassword', 'Current password is required');
      isValid = false;
    }

    if (!Validator.validateRequired(newPassword)) {
      this.showFieldError('newPassword', 'New password is required');
      isValid = false;
    } else if (!Validator.validatePassword(newPassword)) {
      this.showFieldError('newPassword', 'Password must be at least 8 characters with at least one letter and one number');
      isValid = false;
    }

    return isValid;
  }

  showFieldError(fieldName, message) {
    const errorElement = this.container.querySelector(`#${fieldName}-error`);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.classList.remove('hidden');
    }
  }

  showError(formType, message) {
    const errorElement = this.container.querySelector(`#${formType}-error`);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.classList.remove('hidden');
    }
  }

  showSuccess(formType, message) {
    const successElement = this.container.querySelector(`#${formType}-success`);
    if (successElement) {
      successElement.textContent = message;
      successElement.classList.remove('hidden');
    }
  }

  clearErrors(formType) {
    const errorElements = this.container.querySelectorAll(`#${formType}-error, #${formType}-success, .error-message`);
    errorElements.forEach(element => {
      element.classList.add('hidden');
      element.textContent = '';
    });
  }

  setLoading(buttonType, loading) {
    const btn = this.container.querySelector(`#${buttonType === 'save' ? 'save-profile-btn' : 'change-password-btn'}`);
    const text = this.container.querySelector(`#${buttonType}-text`);
    const loadingText = this.container.querySelector(`#${buttonType}-loading`);

    if (btn && text && loadingText) {
      btn.disabled = loading;
      
      if (loading) {
        text.classList.add('hidden');
        loadingText.classList.remove('hidden');
      } else {
        text.classList.remove('hidden');
        loadingText.classList.add('hidden');
      }
    }
  }
}
