export class AccessibilityUtils {
  // Focus management
  static trapFocus(element) {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstFocusable) {
          lastFocusable.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastFocusable) {
          firstFocusable.focus();
          e.preventDefault();
        }
      }
    };

    element.addEventListener('keydown', handleTabKey);
    
    // Focus first element
    if (firstFocusable) {
      firstFocusable.focus();
    }

    // Return cleanup function
    return () => {
      element.removeEventListener('keydown', handleTabKey);
    };
  }

  static restoreFocus(previousActiveElement) {
    if (previousActiveElement && typeof previousActiveElement.focus === 'function') {
      previousActiveElement.focus();
    }
  }

  // ARIA utilities
  static setAriaExpanded(element, expanded) {
    element.setAttribute('aria-expanded', expanded.toString());
  }

  static setAriaHidden(element, hidden) {
    element.setAttribute('aria-hidden', hidden.toString());
  }

  static setAriaLabel(element, label) {
    element.setAttribute('aria-label', label);
  }

  static setAriaDescribedBy(element, id) {
    element.setAttribute('aria-describedby', id);
  }

  static setAriaLabelledBy(element, id) {
    element.setAttribute('aria-labelledby', id);
  }

  // Live regions
  static announceToScreenReader(message, priority = 'polite') {
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', priority);
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.textContent = message;

    document.body.appendChild(liveRegion);

    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(liveRegion);
    }, 1000);
  }

  // Keyboard navigation
  static handleArrowKeys(elements, currentIndex, callback) {
    return (e) => {
      let newIndex = currentIndex;

      switch (e.key) {
        case 'ArrowDown':
        case 'ArrowRight':
          newIndex = (currentIndex + 1) % elements.length;
          e.preventDefault();
          break;
        case 'ArrowUp':
        case 'ArrowLeft':
          newIndex = currentIndex === 0 ? elements.length - 1 : currentIndex - 1;
          e.preventDefault();
          break;
        case 'Home':
          newIndex = 0;
          e.preventDefault();
          break;
        case 'End':
          newIndex = elements.length - 1;
          e.preventDefault();
          break;
        default:
          return;
      }

      elements[newIndex].focus();
      if (callback) callback(newIndex);
    };
  }

  // Skip links
  static createSkipLink(targetId, text = 'Skip to main content') {
    const skipLink = document.createElement('a');
    skipLink.href = `#${targetId}`;
    skipLink.textContent = text;
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50';
    
    skipLink.addEventListener('click', (e) => {
      e.preventDefault();
      const target = document.getElementById(targetId);
      if (target) {
        target.focus();
        target.scrollIntoView();
      }
    });

    return skipLink;
  }

  // Color contrast utilities
  static checkColorContrast(foreground, background) {
    // Simple contrast ratio calculation
    const getLuminance = (color) => {
      const rgb = parseInt(color.slice(1), 16);
      const r = (rgb >> 16) & 0xff;
      const g = (rgb >> 8) & 0xff;
      const b = (rgb >> 0) & 0xff;
      
      const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    };

    const l1 = getLuminance(foreground);
    const l2 = getLuminance(background);
    const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
    
    return {
      ratio,
      aa: ratio >= 4.5,
      aaa: ratio >= 7
    };
  }

  // Reduced motion utilities
  static prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  static respectReducedMotion(element, animationClass) {
    if (!this.prefersReducedMotion()) {
      element.classList.add(animationClass);
    }
  }

  // High contrast utilities
  static prefersHighContrast() {
    return window.matchMedia('(prefers-contrast: high)').matches;
  }

  // Screen reader utilities
  static hideFromScreenReader(element) {
    element.setAttribute('aria-hidden', 'true');
  }

  static showToScreenReader(element) {
    element.removeAttribute('aria-hidden');
  }

  static makeScreenReaderOnly(element) {
    element.className += ' sr-only';
  }

  // Form accessibility
  static associateLabelWithInput(label, input) {
    const id = input.id || `input-${Date.now()}`;
    input.id = id;
    label.setAttribute('for', id);
  }

  static addErrorToInput(input, errorMessage) {
    const errorId = `${input.id}-error`;
    let errorElement = document.getElementById(errorId);
    
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.id = errorId;
      errorElement.className = 'error-message text-red-600 text-sm mt-1';
      input.parentNode.insertBefore(errorElement, input.nextSibling);
    }
    
    errorElement.textContent = errorMessage;
    input.setAttribute('aria-describedby', errorId);
    input.setAttribute('aria-invalid', 'true');
  }

  static removeErrorFromInput(input) {
    const errorId = `${input.id}-error`;
    const errorElement = document.getElementById(errorId);
    
    if (errorElement) {
      errorElement.remove();
    }
    
    input.removeAttribute('aria-describedby');
    input.removeAttribute('aria-invalid');
  }

  // Modal accessibility
  static makeModalAccessible(modal, trigger) {
    const previousActiveElement = document.activeElement;
    
    // Set ARIA attributes
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');
    
    // Trap focus
    const cleanup = this.trapFocus(modal);
    
    // Handle escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal();
      }
    };
    
    const closeModal = () => {
      cleanup();
      document.removeEventListener('keydown', handleEscape);
      this.restoreFocus(previousActiveElement);
    };
    
    document.addEventListener('keydown', handleEscape);
    
    return closeModal;
  }
}
