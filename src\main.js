import './style.css';
import { AppRouter } from './router/Router.js';
import { Router } from './utils/auth.js';
import { ErrorHandler } from './utils/errorHandler.js';

// Initialize the application
class App {
  constructor() {
    this.router = null;
    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.startApp();
      });
    } else {
      this.startApp();
    }
  }

  startApp() {
    try {
      // Initialize router
      this.router = new AppRouter();

      // Override Router.navigate to use AppRouter
      Router.navigate = (path) => {
        this.router.navigate(path);
      };
    } catch (error) {
      ErrorHandler.handleError(error, 'App initialization');
    }
  }
}

// Start the application
new App();
