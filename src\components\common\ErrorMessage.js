export class ErrorMessage {
  static render(message, type = 'error') {
    const bgColor = type === 'error' ? 'bg-red-100 border-red-400 text-red-700' : 'bg-yellow-100 border-yellow-400 text-yellow-700';
    
    return `
      <div class="${bgColor} px-4 py-3 rounded-lg border">
        <div class="flex">
          <div class="flex-shrink-0">
            ${type === 'error' ? `
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            ` : `
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            `}
          </div>
          <div class="ml-3">
            <p class="text-sm">${message}</p>
          </div>
        </div>
      </div>
    `;
  }

  static renderFullPage(message, type = 'error') {
    return `
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full">
          ${this.render(message, type)}
        </div>
      </div>
    `;
  }
}
