import { ENV, Logger } from '../config/env.js';

export class Analytics {
  static isEnabled = ENV.IS_PRODUCTION;
  static queue = [];
  static initialized = false;

  static init(config = {}) {
    if (this.initialized) return;
    
    this.config = {
      trackPageViews: true,
      trackClicks: true,
      trackFormSubmissions: true,
      trackErrors: true,
      ...config
    };

    if (this.isEnabled) {
      this.setupTracking();
    }
    
    this.initialized = true;
    Logger.info('Analytics initialized');
  }

  static setupTracking() {
    if (this.config.trackPageViews) {
      this.trackPageView();
      window.addEventListener('popstate', () => this.trackPageView());
    }

    if (this.config.trackClicks) {
      document.addEventListener('click', this.handleClick.bind(this));
    }

    if (this.config.trackFormSubmissions) {
      document.addEventListener('submit', this.handleFormSubmit.bind(this));
    }

    if (this.config.trackErrors) {
      window.addEventListener('error', this.handleError.bind(this));
      window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
    }
  }

  static track(event, properties = {}) {
    const data = {
      event,
      properties: {
        ...properties,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        referrer: document.referrer
      }
    };

    if (this.isEnabled) {
      this.send(data);
    } else {
      Logger.log('Analytics event:', data);
    }
  }

  static trackPageView(path = window.location.pathname) {
    this.track('page_view', {
      path,
      title: document.title,
      search: window.location.search,
      hash: window.location.hash
    });
  }

  static trackEvent(category, action, label = null, value = null) {
    this.track('custom_event', {
      category,
      action,
      label,
      value
    });
  }

  static trackTiming(category, variable, time, label = null) {
    this.track('timing', {
      category,
      variable,
      time,
      label
    });
  }

  static trackUser(userId, properties = {}) {
    this.track('identify_user', {
      userId,
      ...properties
    });
  }

  static trackConversion(goal, value = null) {
    this.track('conversion', {
      goal,
      value
    });
  }

  static handleClick(event) {
    const element = event.target.closest('a, button, [data-track]');
    if (!element) return;

    const trackingData = element.dataset.track;
    if (trackingData) {
      try {
        const data = JSON.parse(trackingData);
        this.track('click', data);
      } catch {
        this.track('click', { element: trackingData });
      }
    } else {
      this.track('click', {
        element: element.tagName.toLowerCase(),
        text: element.textContent?.trim().substring(0, 100),
        href: element.href,
        id: element.id,
        className: element.className
      });
    }
  }

  static handleFormSubmit(event) {
    const form = event.target;
    const formData = new FormData(form);
    const data = {};
    
    // Don't track sensitive data
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    
    for (const [key, value] of formData.entries()) {
      if (!sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        data[key] = typeof value === 'string' ? value.substring(0, 100) : '[file]';
      }
    }

    this.track('form_submit', {
      formId: form.id,
      formAction: form.action,
      formMethod: form.method,
      fields: Object.keys(data),
      fieldCount: Object.keys(data).length
    });
  }

  static handleError(event) {
    this.track('javascript_error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack?.substring(0, 1000)
    });
  }

  static handleUnhandledRejection(event) {
    this.track('unhandled_promise_rejection', {
      reason: event.reason?.toString().substring(0, 1000)
    });
  }

  static send(data) {
    // In a real implementation, you would send this to your analytics service
    // For example: Google Analytics, Mixpanel, Amplitude, etc.
    
    if (navigator.sendBeacon) {
      // Use sendBeacon for reliability
      navigator.sendBeacon('/analytics', JSON.stringify(data));
    } else {
      // Fallback to fetch
      fetch('/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      }).catch(error => {
        Logger.error('Analytics send failed:', error);
      });
    }
  }

  // Performance tracking
  static trackPerformance() {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      if (navigation) {
        this.track('performance', {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: this.getFirstPaint(),
          firstContentfulPaint: this.getFirstContentfulPaint()
        });
      }
    }
  }

  static getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  static getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return firstContentfulPaint ? firstContentfulPaint.startTime : null;
  }

  // A/B Testing support
  static getVariant(testName, variants = ['A', 'B']) {
    const key = `ab_test_${testName}`;
    let variant = localStorage.getItem(key);
    
    if (!variant) {
      variant = variants[Math.floor(Math.random() * variants.length)];
      localStorage.setItem(key, variant);
    }
    
    this.track('ab_test_assignment', {
      testName,
      variant
    });
    
    return variant;
  }

  // Heatmap tracking
  static trackHeatmap(event) {
    if (event.type === 'click') {
      this.track('heatmap_click', {
        x: event.clientX,
        y: event.clientY,
        element: event.target.tagName.toLowerCase(),
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      });
    }
  }

  // Session tracking
  static startSession() {
    const sessionId = this.generateSessionId();
    sessionStorage.setItem('analytics_session_id', sessionId);
    
    this.track('session_start', {
      sessionId
    });
    
    return sessionId;
  }

  static endSession() {
    const sessionId = sessionStorage.getItem('analytics_session_id');
    
    if (sessionId) {
      this.track('session_end', {
        sessionId
      });
      
      sessionStorage.removeItem('analytics_session_id');
    }
  }

  static generateSessionId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Utility methods
  static disable() {
    this.isEnabled = false;
    Logger.info('Analytics disabled');
  }

  static enable() {
    this.isEnabled = true;
    Logger.info('Analytics enabled');
  }

  static flush() {
    // Send any queued events
    if (this.queue.length > 0) {
      this.queue.forEach(data => this.send(data));
      this.queue = [];
    }
  }
}

// Auto-initialize on page load
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    Analytics.init();
    
    // Track performance after page load
    window.addEventListener('load', () => {
      setTimeout(() => Analytics.trackPerformance(), 0);
    });
    
    // Track session end on page unload
    window.addEventListener('beforeunload', () => {
      Analytics.endSession();
      Analytics.flush();
    });
  });
}
