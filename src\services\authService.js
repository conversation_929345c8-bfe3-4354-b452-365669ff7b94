import { apiClient } from '../config/api.js';
import { API_CONFIG } from '../config/api.js';
import { AuthManager } from '../utils/auth.js';

export class AuthService {
  static async register(email, password) {
    try {
      const response = await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.REGISTER, {
        email,
        password
      });

      if (response.success) {
        AuthManager.login(response.data.token, response.data.user);
        return response;
      }
      throw new Error(response.message || 'Registration failed');
    } catch (error) {
      throw error;
    }
  }

  static async login(email, password) {
    try {
      const response = await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGIN, {
        email,
        password
      });

      if (response.success) {
        AuthManager.login(response.data.token, response.data.user);
        return response;
      }
      throw new Error(response.message || 'Login failed');
    } catch (error) {
      throw error;
    }
  }

  static async logout() {
    try {
      await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      AuthManager.logout();
    }
  }

  static async getProfile() {
    try {
      const response = await apiClient.get(API_CONFIG.ENDPOINTS.AUTH.PROFILE);
      return response;
    } catch (error) {
      throw error;
    }
  }

  static async updateProfile(profileData) {
    try {
      const response = await apiClient.put(API_CONFIG.ENDPOINTS.AUTH.PROFILE, profileData);
      if (response.success) {
        // Update user data in localStorage
        AuthManager.setUser(response.data.user);
      }
      return response;
    } catch (error) {
      throw error;
    }
  }

  static async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.CHANGE_PASSWORD, {
        currentPassword,
        newPassword
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  static async deleteAccount() {
    try {
      const response = await apiClient.delete(API_CONFIG.ENDPOINTS.AUTH.DELETE_ACCOUNT);
      if (response.success) {
        AuthManager.logout();
      }
      return response;
    } catch (error) {
      throw error;
    }
  }
}
