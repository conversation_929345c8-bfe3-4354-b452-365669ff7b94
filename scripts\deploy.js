#!/usr/bin/env node

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Deployment configuration
const config = {
  environments: {
    staging: {
      branch: 'develop',
      url: 'https://staging.petatalenta.com',
      buildCommand: 'npm run build:staging'
    },
    production: {
      branch: 'main',
      url: 'https://petatalenta.com',
      buildCommand: 'npm run build'
    }
  },
  distDir: 'dist'
};

const environment = process.argv[2] || 'staging';
const envConfig = config.environments[environment];

if (!envConfig) {
  console.error(`❌ Unknown environment: ${environment}`);
  console.log('Available environments:', Object.keys(config.environments).join(', '));
  process.exit(1);
}

console.log(`🚀 Deploying to ${environment}...`);

try {
  // Pre-deployment checks
  console.log('🔍 Running pre-deployment checks...');
  
  // Check if we're on the correct branch
  const currentBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  if (currentBranch !== envConfig.branch) {
    console.warn(`⚠️ Warning: Current branch (${currentBranch}) doesn't match target branch (${envConfig.branch})`);
    
    if (!process.argv.includes('--force')) {
      console.log('Use --force to deploy anyway');
      process.exit(1);
    }
  }

  // Check for uncommitted changes
  try {
    execSync('git diff-index --quiet HEAD --', { stdio: 'ignore' });
  } catch {
    console.warn('⚠️ Warning: You have uncommitted changes');
    
    if (!process.argv.includes('--force')) {
      console.log('Commit your changes or use --force to deploy anyway');
      process.exit(1);
    }
  }

  // Run tests
  if (!process.argv.includes('--skip-tests')) {
    console.log('🧪 Running tests...');
    try {
      execSync('npm test', { cwd: rootDir, stdio: 'inherit' });
    } catch (error) {
      console.error('❌ Tests failed');
      process.exit(1);
    }
  }

  // Build application
  console.log('📦 Building application...');
  execSync(envConfig.buildCommand, { 
    cwd: rootDir, 
    stdio: 'inherit',
    env: { 
      ...process.env, 
      NODE_ENV: 'production',
      DEPLOY_ENV: environment
    }
  });

  // Verify build
  console.log('✅ Verifying build...');
  if (!existsSync(join(rootDir, config.distDir, 'index.html'))) {
    throw new Error('Build verification failed: index.html not found');
  }

  // Deploy based on deployment method
  const deployMethod = process.env.DEPLOY_METHOD || 'netlify';
  
  switch (deployMethod) {
    case 'netlify':
      deployToNetlify();
      break;
    case 'vercel':
      deployToVercel();
      break;
    case 'github-pages':
      deployToGitHubPages();
      break;
    case 'ftp':
      deployToFTP();
      break;
    case 's3':
      deployToS3();
      break;
    default:
      console.log('📁 Build completed. Manual deployment required.');
      console.log(`Deploy the contents of ${config.distDir}/ to your server`);
  }

  // Post-deployment tasks
  console.log('🔧 Running post-deployment tasks...');
  
  // Warm up the site
  if (envConfig.url) {
    console.log('🌡️ Warming up the site...');
    try {
      execSync(`curl -s ${envConfig.url} > /dev/null`, { stdio: 'ignore' });
      console.log('  ✓ Site is responding');
    } catch {
      console.warn('  ⚠️ Site warmup failed');
    }
  }

  // Update deployment record
  updateDeploymentRecord();

  console.log(`✅ Deployment to ${environment} completed successfully!`);
  if (envConfig.url) {
    console.log(`🌐 Site URL: ${envConfig.url}`);
  }

} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  process.exit(1);
}

// Deployment methods
function deployToNetlify() {
  console.log('🌐 Deploying to Netlify...');
  
  if (!process.env.NETLIFY_AUTH_TOKEN) {
    throw new Error('NETLIFY_AUTH_TOKEN environment variable is required');
  }

  const siteId = process.env.NETLIFY_SITE_ID;
  if (!siteId) {
    throw new Error('NETLIFY_SITE_ID environment variable is required');
  }

  execSync(`npx netlify deploy --prod --dir=${config.distDir} --site=${siteId}`, {
    cwd: rootDir,
    stdio: 'inherit',
    env: { ...process.env, NETLIFY_AUTH_TOKEN: process.env.NETLIFY_AUTH_TOKEN }
  });
}

function deployToVercel() {
  console.log('▲ Deploying to Vercel...');
  
  const prodFlag = environment === 'production' ? '--prod' : '';
  execSync(`npx vercel deploy ${prodFlag} --yes`, {
    cwd: rootDir,
    stdio: 'inherit'
  });
}

function deployToGitHubPages() {
  console.log('📄 Deploying to GitHub Pages...');
  
  // This assumes you have gh-pages package installed
  execSync(`npx gh-pages -d ${config.distDir}`, {
    cwd: rootDir,
    stdio: 'inherit'
  });
}

function deployToFTP() {
  console.log('📤 Deploying via FTP...');
  
  const ftpConfig = {
    host: process.env.FTP_HOST,
    user: process.env.FTP_USER,
    password: process.env.FTP_PASSWORD,
    remotePath: process.env.FTP_REMOTE_PATH || '/'
  };

  if (!ftpConfig.host || !ftpConfig.user || !ftpConfig.password) {
    throw new Error('FTP credentials (FTP_HOST, FTP_USER, FTP_PASSWORD) are required');
  }

  // This would require an FTP deployment tool
  console.log('  💡 FTP deployment requires additional tooling');
  console.log('  Consider using tools like lftp or a Node.js FTP client');
}

function deployToS3() {
  console.log('☁️ Deploying to AWS S3...');
  
  const bucket = process.env.S3_BUCKET;
  if (!bucket) {
    throw new Error('S3_BUCKET environment variable is required');
  }

  // Sync files to S3
  execSync(`aws s3 sync ${config.distDir}/ s3://${bucket}/ --delete`, {
    cwd: rootDir,
    stdio: 'inherit'
  });

  // Invalidate CloudFront cache if distribution ID is provided
  const distributionId = process.env.CLOUDFRONT_DISTRIBUTION_ID;
  if (distributionId) {
    console.log('🔄 Invalidating CloudFront cache...');
    execSync(`aws cloudfront create-invalidation --distribution-id ${distributionId} --paths "/*"`, {
      stdio: 'inherit'
    });
  }
}

function updateDeploymentRecord() {
  const deploymentRecord = {
    environment,
    timestamp: new Date().toISOString(),
    commit: getGitCommit(),
    branch: getGitBranch(),
    version: getPackageVersion(),
    url: envConfig.url
  };

  const recordsFile = join(rootDir, 'deployments.json');
  let records = [];
  
  if (existsSync(recordsFile)) {
    try {
      records = JSON.parse(readFileSync(recordsFile, 'utf8'));
    } catch {
      records = [];
    }
  }

  records.unshift(deploymentRecord);
  
  // Keep only last 50 deployments
  records = records.slice(0, 50);
  
  writeFileSync(recordsFile, JSON.stringify(records, null, 2));
  console.log('  ✓ Updated deployment record');
}

function getGitCommit() {
  try {
    return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return 'unknown';
  }
}

function getGitBranch() {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  } catch {
    return 'unknown';
  }
}

function getPackageVersion() {
  try {
    const packageJson = JSON.parse(readFileSync(join(rootDir, 'package.json'), 'utf8'));
    return packageJson.version;
  } catch {
    return '1.0.0';
  }
}
