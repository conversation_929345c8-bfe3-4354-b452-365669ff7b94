# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
dist-ssr/
build/
.vite/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs
logs/
*.log

# Coverage and testing
coverage/
*.lcov
.nyc_output/
test-results/
playwright-report/

# Cache directories
.cache/
.parcel-cache/
.eslintcache

# Temporary folders
tmp/
temp/

# Deployment records
deployments.json

# Local development files
*.local
