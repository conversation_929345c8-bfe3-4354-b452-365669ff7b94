# API Documentation

Base URL: `https://api.chhrone.web.id`

## Authentication Endpoints

### Register User
**POST** `/api/auth/register`

Register a new user to the system.

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "myPassword1"
}
```

**Validation Rules:**
- `email`: Valid email format, maximum 255 characters, required
- `password`: Minimum 8 characters, must contain at least one letter and one number, required

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "username": null,
      "user_type": "user",
      "is_active": true,
      "token_balance": 5,
      "created_at": "2024-01-15T10:30:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "User registered successfully"
}
```

### User Login
**POST** `/api/auth/login`

Login user and get JWT token.

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "myPassword1"
}
```

**Validation Rules:**
- `email`: Valid email format, required
- `password`: Required (no specific format validation for login)

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "username": "johndoe",
      "user_type": "user",
      "is_active": true,
      "token_balance": 5
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Login successful"
}
```

### Get User Profile
**GET** `/api/auth/profile`

Get profile of the currently logged-in user.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "username": "johndoe",
      "user_type": "user",
      "is_active": true,
      "token_balance": 5,
      "created_at": "2024-01-15T10:30:00.000Z",
      "updated_at": "2024-01-15T10:30:00.000Z"
    },
    "profile": {
      "full_name": "John Doe",
      "date_of_birth": "1990-01-15",
      "gender": "male",
      "school_id": 1
    }
  }
}
```

### Update User Profile
**PUT** `/api/auth/profile`

Update user profile.

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "username": "johndoe",
  "full_name": "John Doe",
  "school_id": 1,
  "date_of_birth": "1990-01-15",
  "gender": "male"
}
```

**Validation Rules:**
- `username`: Alphanumeric only, 3-100 characters, optional
- `email`: Valid email format, maximum 255 characters, optional
- `full_name`: Maximum 100 characters, optional
- `school_id`: Positive integer, optional
- `date_of_birth`: ISO date format (YYYY-MM-DD), cannot be future date, optional
- `gender`: Must be one of: "male", "female", optional

### Change Password
**POST** `/api/auth/change-password`

Change user password.

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "currentPassword": "oldPassword1",
  "newPassword": "newPassword2"
}
```

**Validation Rules:**
- `currentPassword`: Required
- `newPassword`: Minimum 8 characters, must contain at least one letter and one number, required

### Logout
**POST** `/api/auth/logout`

Logout user from the system.

**Headers:**
```
Authorization: Bearer <token>
```

### Delete User Account
**DELETE** `/api/auth/account`

Delete user account completely (soft delete).

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Account deleted successfully",
  "data": {
    "deletedAt": "2024-01-15T10:30:00.000Z",
    "originalEmail": "<EMAIL>"
  }
}
```

## Rate Limits

- **Auth Limiter**: 100 requests per 15 minutes for authentication endpoints
- **General Gateway**: 5000 requests per 15 minutes for authenticated endpoints

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid or missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `429`: Too Many Requests (rate limit exceeded)
- `500`: Internal Server Error
