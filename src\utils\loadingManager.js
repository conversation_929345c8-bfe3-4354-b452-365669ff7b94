export class LoadingManager {
  static activeLoaders = new Set();
  static overlay = null;

  static show(id = 'default', message = 'Loading...') {
    this.activeLoaders.add(id);
    this.createOverlay(message);
    this.showOverlay();
  }

  static hide(id = 'default') {
    this.activeLoaders.delete(id);
    
    if (this.activeLoaders.size === 0) {
      this.hideOverlay();
    }
  }

  static createOverlay(message) {
    if (!this.overlay) {
      this.overlay = document.createElement('div');
      this.overlay.id = 'global-loading-overlay';
      this.overlay.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 opacity-0 transition-opacity duration-300';
      
      this.overlay.innerHTML = `
        <div class="bg-white rounded-lg p-6 shadow-lg">
          <div class="flex items-center space-x-3">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="text-gray-700 font-medium" id="loading-message">${message}</span>
          </div>
        </div>
      `;
      
      document.body.appendChild(this.overlay);
    } else {
      const messageElement = this.overlay.querySelector('#loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
    }
  }

  static showOverlay() {
    if (this.overlay) {
      this.overlay.style.display = 'flex';
      setTimeout(() => {
        this.overlay.classList.remove('opacity-0');
      }, 10);
    }
  }

  static hideOverlay() {
    if (this.overlay) {
      this.overlay.classList.add('opacity-0');
      setTimeout(() => {
        if (this.overlay && this.activeLoaders.size === 0) {
          this.overlay.style.display = 'none';
        }
      }, 300);
    }
  }

  static isLoading(id = null) {
    if (id) {
      return this.activeLoaders.has(id);
    }
    return this.activeLoaders.size > 0;
  }

  static clear() {
    this.activeLoaders.clear();
    this.hideOverlay();
  }

  // Utility method for wrapping async operations
  static async wrap(asyncFn, id = 'default', message = 'Loading...') {
    try {
      this.show(id, message);
      const result = await asyncFn();
      return result;
    } finally {
      this.hide(id);
    }
  }
}
