export class SecurityUtils {
  // XSS Prevention
  static sanitizeHTML(html) {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  }

  static escapeHTML(text) {
    const div = document.createElement('div');
    div.appendChild(document.createTextNode(text));
    return div.innerHTML;
  }

  static stripHTML(html) {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  }

  // Input validation
  static validateInput(input, type) {
    switch (type) {
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
      case 'url':
        try {
          new URL(input);
          return true;
        } catch {
          return false;
        }
      case 'alphanumeric':
        return /^[a-zA-Z0-9]+$/.test(input);
      case 'numeric':
        return /^\d+$/.test(input);
      case 'phone':
        return /^\+?[\d\s\-\(\)]+$/.test(input);
      default:
        return true;
    }
  }

  // CSRF Protection
  static generateCSRFToken() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  static setCSRFToken(token) {
    const meta = document.createElement('meta');
    meta.name = 'csrf-token';
    meta.content = token;
    document.head.appendChild(meta);
  }

  static getCSRFToken() {
    const meta = document.querySelector('meta[name="csrf-token"]');
    return meta ? meta.content : null;
  }

  // Content Security Policy helpers
  static createNonce() {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  static addNonceToScript(script, nonce) {
    script.setAttribute('nonce', nonce);
  }

  // Secure random generation
  static generateSecureRandom(length = 32) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return array;
  }

  static generateSecureRandomString(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => chars[byte % chars.length]).join('');
  }

  // Password utilities
  static generateSecurePassword(length = 16) {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const allChars = lowercase + uppercase + numbers + symbols;

    let password = '';
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  static checkPasswordStrength(password) {
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      symbols: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password),
      noCommon: !this.isCommonPassword(password)
    };

    const score = Object.values(checks).filter(Boolean).length;
    
    let strength = 'Very Weak';
    if (score >= 6) strength = 'Very Strong';
    else if (score >= 5) strength = 'Strong';
    else if (score >= 4) strength = 'Medium';
    else if (score >= 3) strength = 'Weak';

    return { checks, score, strength };
  }

  static isCommonPassword(password) {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    return commonPasswords.includes(password.toLowerCase());
  }

  // Token utilities
  static isTokenExpired(token) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  static getTokenPayload(token) {
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch {
      return null;
    }
  }

  // Secure storage
  static secureStore(key, value, encrypt = false) {
    try {
      let dataToStore = value;
      
      if (encrypt && crypto.subtle) {
        // Note: This is a simplified example. In production, use proper encryption
        dataToStore = btoa(JSON.stringify(value));
      }
      
      localStorage.setItem(key, typeof dataToStore === 'string' ? dataToStore : JSON.stringify(dataToStore));
      return true;
    } catch (error) {
      console.error('Secure store failed:', error);
      return false;
    }
  }

  static secureRetrieve(key, decrypt = false) {
    try {
      const data = localStorage.getItem(key);
      if (!data) return null;
      
      if (decrypt) {
        return JSON.parse(atob(data));
      }
      
      try {
        return JSON.parse(data);
      } catch {
        return data;
      }
    } catch (error) {
      console.error('Secure retrieve failed:', error);
      return null;
    }
  }

  // Rate limiting
  static createRateLimiter(maxRequests, windowMs) {
    const requests = new Map();
    
    return (identifier) => {
      const now = Date.now();
      const windowStart = now - windowMs;
      
      // Clean old requests
      for (const [id, timestamps] of requests.entries()) {
        requests.set(id, timestamps.filter(time => time > windowStart));
        if (requests.get(id).length === 0) {
          requests.delete(id);
        }
      }
      
      // Check current identifier
      const userRequests = requests.get(identifier) || [];
      
      if (userRequests.length >= maxRequests) {
        return false; // Rate limited
      }
      
      userRequests.push(now);
      requests.set(identifier, userRequests);
      return true; // Allowed
    };
  }

  // Clickjacking protection
  static preventClickjacking() {
    if (window.top !== window.self) {
      // Page is in an iframe
      document.body.style.display = 'none';
      throw new Error('Clickjacking attempt detected');
    }
  }

  // Secure headers check
  static checkSecurityHeaders() {
    const warnings = [];
    
    // Check if HTTPS
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      warnings.push('Site is not served over HTTPS');
    }
    
    // Check CSP
    const csp = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (!csp) {
      warnings.push('Content Security Policy not found');
    }
    
    return warnings;
  }

  // Input sanitization for different contexts
  static sanitizeForAttribute(value) {
    return value.replace(/['"<>&]/g, (match) => {
      const entities = {
        '"': '&quot;',
        "'": '&#x27;',
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;'
      };
      return entities[match];
    });
  }

  static sanitizeForURL(value) {
    return encodeURIComponent(value);
  }

  static sanitizeForCSS(value) {
    return value.replace(/[<>"'&\\]/g, '\\$&');
  }

  // Timing attack prevention
  static constantTimeCompare(a, b) {
    if (a.length !== b.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }
    
    return result === 0;
  }
}
