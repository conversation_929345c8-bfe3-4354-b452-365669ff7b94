import { Logger } from '../config/env.js';

export class ErrorHandler {
  static handleError(error, context = 'Unknown') {
    Logger.error(`Error in ${context}:`, error);
    
    // Log to external service in production
    if (import.meta.env.PROD) {
      this.logToService(error, context);
    }
    
    return this.getErrorMessage(error);
  }

  static getErrorMessage(error) {
    if (error.message) {
      return error.message;
    }
    
    if (typeof error === 'string') {
      return error;
    }
    
    return 'An unexpected error occurred';
  }

  static logToService(error, context) {
    // Implement external logging service here
    // Example: Sentry, LogRocket, etc.
    console.log('Would log to external service:', { error, context });
  }

  static setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      Logger.error('Unhandled promise rejection:', event.reason);
      event.preventDefault();
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      Logger.error('Global error:', event.error);
    });
  }
}

// Initialize global error handlers
ErrorHandler.setupGlobalErrorHandlers();
