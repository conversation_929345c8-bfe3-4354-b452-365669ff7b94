export class ResponsiveUtils {
  static breakpoints = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536
  };

  static getCurrentBreakpoint() {
    const width = window.innerWidth;
    
    if (width >= this.breakpoints['2xl']) return '2xl';
    if (width >= this.breakpoints.xl) return 'xl';
    if (width >= this.breakpoints.lg) return 'lg';
    if (width >= this.breakpoints.md) return 'md';
    if (width >= this.breakpoints.sm) return 'sm';
    return 'xs';
  }

  static isMobile() {
    return window.innerWidth < this.breakpoints.md;
  }

  static isTablet() {
    const width = window.innerWidth;
    return width >= this.breakpoints.md && width < this.breakpoints.lg;
  }

  static isDesktop() {
    return window.innerWidth >= this.breakpoints.lg;
  }

  static onBreakpointChange(callback) {
    let currentBreakpoint = this.getCurrentBreakpoint();
    
    const handleResize = () => {
      const newBreakpoint = this.getCurrentBreakpoint();
      if (newBreakpoint !== currentBreakpoint) {
        currentBreakpoint = newBreakpoint;
        callback(newBreakpoint);
      }
    };

    window.addEventListener('resize', handleResize);
    
    // Return cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }

  static matchMedia(query) {
    return window.matchMedia(query);
  }

  static onMediaChange(query, callback) {
    const mediaQuery = this.matchMedia(query);
    mediaQuery.addEventListener('change', callback);
    
    // Call immediately with current state
    callback(mediaQuery);
    
    // Return cleanup function
    return () => {
      mediaQuery.removeEventListener('change', callback);
    };
  }

  // Utility for responsive navigation
  static setupResponsiveNav(mobileMenuSelector, toggleSelector) {
    const mobileMenu = document.querySelector(mobileMenuSelector);
    const toggle = document.querySelector(toggleSelector);
    
    if (!mobileMenu || !toggle) return;

    let isOpen = false;

    const toggleMenu = () => {
      isOpen = !isOpen;
      mobileMenu.classList.toggle('hidden', !isOpen);
      toggle.setAttribute('aria-expanded', isOpen.toString());
    };

    const closeMenu = () => {
      isOpen = false;
      mobileMenu.classList.add('hidden');
      toggle.setAttribute('aria-expanded', 'false');
    };

    toggle.addEventListener('click', toggleMenu);

    // Close menu on desktop
    this.onBreakpointChange((breakpoint) => {
      if (this.isDesktop()) {
        closeMenu();
      }
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (isOpen && !mobileMenu.contains(e.target) && !toggle.contains(e.target)) {
        closeMenu();
      }
    });

    return { toggleMenu, closeMenu };
  }

  // Utility for responsive images
  static setupResponsiveImages() {
    const images = document.querySelectorAll('img[data-responsive]');
    
    images.forEach(img => {
      const sources = JSON.parse(img.dataset.responsive);
      
      const updateImage = () => {
        const breakpoint = this.getCurrentBreakpoint();
        const source = sources[breakpoint] || sources.default || img.src;
        
        if (img.src !== source) {
          img.src = source;
        }
      };

      updateImage();
      this.onBreakpointChange(updateImage);
    });
  }

  // Utility for responsive text
  static setupResponsiveText() {
    const elements = document.querySelectorAll('[data-responsive-text]');
    
    elements.forEach(element => {
      const texts = JSON.parse(element.dataset.responsiveText);
      
      const updateText = () => {
        const breakpoint = this.getCurrentBreakpoint();
        const text = texts[breakpoint] || texts.default || element.textContent;
        
        if (element.textContent !== text) {
          element.textContent = text;
        }
      };

      updateText();
      this.onBreakpointChange(updateText);
    });
  }

  // Touch device detection
  static isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  // Viewport utilities
  static getViewportSize() {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }

  static getScrollPosition() {
    return {
      x: window.pageXOffset || document.documentElement.scrollLeft,
      y: window.pageYOffset || document.documentElement.scrollTop
    };
  }

  // Orientation utilities
  static getOrientation() {
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
  }

  static onOrientationChange(callback) {
    let currentOrientation = this.getOrientation();
    
    const handleResize = () => {
      const newOrientation = this.getOrientation();
      if (newOrientation !== currentOrientation) {
        currentOrientation = newOrientation;
        callback(newOrientation);
      }
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }
}
