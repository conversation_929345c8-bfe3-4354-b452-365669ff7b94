{"name": "petatalenta-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:staging": "NODE_ENV=production DEPLOY_ENV=staging vite build", "build:production": "NODE_ENV=production DEPLOY_ENV=production vite build", "preview": "vite preview", "test": "echo \"No tests specified\" && exit 0", "lint": "echo \"No linting configured\" && exit 0", "deploy:staging": "node scripts/deploy.js staging", "deploy:production": "node scripts/deploy.js production", "build-custom": "node scripts/build.js", "analyze": "node scripts/build.js --analyze"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.4"}, "dependencies": {"@tailwindcss/vite": "^4.1.11"}}