import { ENV } from '../config/env.js';

export class I18n {
  static currentLanguage = 'en';
  static fallbackLanguage = 'en';
  static translations = {};
  static initialized = false;

  static async init(language = null) {
    if (this.initialized) return;

    this.currentLanguage = language || this.detectLanguage();
    await this.loadTranslations(this.currentLanguage);
    
    this.initialized = true;
    this.updateDocumentLanguage();
  }

  static detectLanguage() {
    // Check localStorage first
    const stored = localStorage.getItem(ENV.STORAGE_KEYS.LANGUAGE || 'language');
    if (stored) return stored;

    // Check browser language
    const browserLang = navigator.language || navigator.languages[0];
    return browserLang.split('-')[0]; // Get language code without region
  }

  static async loadTranslations(language) {
    try {
      // In a real app, you might load from a server or import files
      const translations = await import(`../locales/${language}.js`);
      this.translations[language] = translations.default || translations;
    } catch (error) {
      console.warn(`Failed to load translations for ${language}, using fallback`);
      
      if (language !== this.fallbackLanguage) {
        await this.loadTranslations(this.fallbackLanguage);
      }
    }
  }

  static t(key, params = {}) {
    const translation = this.getTranslation(key);
    return this.interpolate(translation, params);
  }

  static getTranslation(key) {
    const keys = key.split('.');
    let translation = this.translations[this.currentLanguage];

    for (const k of keys) {
      if (translation && typeof translation === 'object' && k in translation) {
        translation = translation[k];
      } else {
        // Try fallback language
        translation = this.translations[this.fallbackLanguage];
        for (const fallbackKey of keys) {
          if (translation && typeof translation === 'object' && fallbackKey in translation) {
            translation = translation[fallbackKey];
          } else {
            return key; // Return key if translation not found
          }
        }
        break;
      }
    }

    return typeof translation === 'string' ? translation : key;
  }

  static interpolate(text, params) {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  static async setLanguage(language) {
    if (language === this.currentLanguage) return;

    await this.loadTranslations(language);
    this.currentLanguage = language;
    
    localStorage.setItem(ENV.STORAGE_KEYS.LANGUAGE || 'language', language);
    this.updateDocumentLanguage();
    this.notifyLanguageChange();
  }

  static updateDocumentLanguage() {
    document.documentElement.lang = this.currentLanguage;
    document.documentElement.dir = this.isRTL() ? 'rtl' : 'ltr';
  }

  static isRTL() {
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.includes(this.currentLanguage);
  }

  static notifyLanguageChange() {
    const event = new CustomEvent('languagechange', {
      detail: { language: this.currentLanguage }
    });
    window.dispatchEvent(event);
  }

  static onLanguageChange(callback) {
    window.addEventListener('languagechange', callback);
  }

  static offLanguageChange(callback) {
    window.removeEventListener('languagechange', callback);
  }

  // Pluralization
  static plural(key, count, params = {}) {
    const pluralKey = count === 1 ? `${key}.one` : `${key}.other`;
    return this.t(pluralKey, { count, ...params });
  }

  // Date and time formatting
  static formatDate(date, options = {}) {
    return new Intl.DateTimeFormat(this.currentLanguage, options).format(date);
  }

  static formatTime(date, options = {}) {
    return new Intl.DateTimeFormat(this.currentLanguage, {
      hour: '2-digit',
      minute: '2-digit',
      ...options
    }).format(date);
  }

  static formatDateTime(date, options = {}) {
    return new Intl.DateTimeFormat(this.currentLanguage, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    }).format(date);
  }

  // Number formatting
  static formatNumber(number, options = {}) {
    return new Intl.NumberFormat(this.currentLanguage, options).format(number);
  }

  static formatCurrency(amount, currency = 'USD', options = {}) {
    return new Intl.NumberFormat(this.currentLanguage, {
      style: 'currency',
      currency,
      ...options
    }).format(amount);
  }

  // Relative time formatting
  static formatRelativeTime(date) {
    const now = new Date();
    const diff = date - now;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (Math.abs(days) > 0) {
      return new Intl.RelativeTimeFormat(this.currentLanguage).format(days, 'day');
    } else if (Math.abs(hours) > 0) {
      return new Intl.RelativeTimeFormat(this.currentLanguage).format(hours, 'hour');
    } else if (Math.abs(minutes) > 0) {
      return new Intl.RelativeTimeFormat(this.currentLanguage).format(minutes, 'minute');
    } else {
      return new Intl.RelativeTimeFormat(this.currentLanguage).format(seconds, 'second');
    }
  }

  // Language selector component
  static createLanguageSelector(languages = ['en', 'es', 'fr']) {
    const select = document.createElement('select');
    select.className = 'language-selector border border-gray-300 rounded px-2 py-1';
    
    languages.forEach(lang => {
      const option = document.createElement('option');
      option.value = lang;
      option.textContent = this.getLanguageName(lang);
      option.selected = lang === this.currentLanguage;
      select.appendChild(option);
    });

    select.addEventListener('change', (e) => {
      this.setLanguage(e.target.value);
    });

    return select;
  }

  static getLanguageName(code) {
    const names = {
      en: 'English',
      es: 'Español',
      fr: 'Français',
      de: 'Deutsch',
      it: 'Italiano',
      pt: 'Português',
      ru: 'Русский',
      ja: '日本語',
      ko: '한국어',
      zh: '中文',
      ar: 'العربية',
      hi: 'हिन्दी'
    };
    return names[code] || code.toUpperCase();
  }

  // Template helpers
  static translateElement(element) {
    const key = element.dataset.i18n;
    if (key) {
      element.textContent = this.t(key);
    }

    const placeholderKey = element.dataset.i18nPlaceholder;
    if (placeholderKey) {
      element.placeholder = this.t(placeholderKey);
    }

    const titleKey = element.dataset.i18nTitle;
    if (titleKey) {
      element.title = this.t(titleKey);
    }
  }

  static translatePage() {
    const elements = document.querySelectorAll('[data-i18n], [data-i18n-placeholder], [data-i18n-title]');
    elements.forEach(element => this.translateElement(element));
  }

  // Utility methods
  static getCurrentLanguage() {
    return this.currentLanguage;
  }

  static getSupportedLanguages() {
    return Object.keys(this.translations);
  }

  static hasTranslation(key) {
    return this.getTranslation(key) !== key;
  }
}

// Auto-initialize
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', async () => {
    await I18n.init();
    I18n.translatePage();
    
    // Re-translate when language changes
    I18n.onLanguageChange(() => {
      I18n.translatePage();
    });
  });
}
