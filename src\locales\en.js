export default {
  // Common
  common: {
    loading: 'Loading...',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    search: 'Search',
    filter: 'Filter',
    clear: 'Clear',
    refresh: 'Refresh',
    retry: 'Retry',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information'
  },

  // Navigation
  nav: {
    home: 'Home',
    dashboard: 'Dashboard',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    login: 'Login',
    register: 'Register'
  },

  // Authentication
  auth: {
    login: {
      title: 'Sign in to your account',
      subtitle: 'Or create a new account',
      email: 'Email address',
      password: 'Password',
      submit: 'Sign in',
      loading: 'Signing in...',
      success: 'Login successful! Redirecting...',
      forgotPassword: 'Forgot your password?'
    },
    register: {
      title: 'Create your account',
      subtitle: 'Or sign in to your existing account',
      email: 'Email address',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      submit: 'Create Account',
      loading: 'Creating Account...',
      success: 'Account created successfully! Redirecting...',
      passwordRequirements: 'Password must contain:',
      requirements: {
        length: 'At least 8 characters',
        letter: 'At least one letter',
        number: 'At least one number'
      }
    },
    profile: {
      title: 'User Profile',
      personalInfo: 'Personal Information',
      username: 'Username',
      fullName: 'Full Name',
      email: 'Email',
      dateOfBirth: 'Date of Birth',
      gender: 'Gender',
      schoolId: 'School ID',
      changePassword: 'Change Password',
      currentPassword: 'Current Password',
      newPassword: 'New Password',
      deleteAccount: 'Delete Account',
      deleteWarning: 'Once you delete your account, there is no going back. Please be certain.',
      updateSuccess: 'Profile updated successfully!',
      passwordChangeSuccess: 'Password changed successfully!',
      deleteConfirm: 'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.'
    },
    logout: {
      confirm: 'Are you sure you want to logout?'
    }
  },

  // Dashboard
  dashboard: {
    title: 'Welcome to Dashboard',
    greeting: 'Hello, {{email}}! This is your dashboard.',
    stats: {
      userType: 'User Type',
      tokenBalance: 'Token Balance',
      accountStatus: 'Account Status',
      active: 'Active',
      inactive: 'Inactive'
    },
    quickActions: {
      title: 'Quick Actions',
      viewProfile: 'View Profile',
      updateSettings: 'Update Settings'
    },
    recentActivity: {
      title: 'Recent Activity',
      noActivity: 'No recent activity to display'
    }
  },

  // Forms
  forms: {
    validation: {
      required: 'This field is required',
      email: 'Please enter a valid email address',
      password: 'Password must be at least 8 characters with at least one letter and one number',
      passwordMismatch: 'Passwords do not match',
      username: 'Username must be 3-100 characters and alphanumeric only',
      fullName: 'Full name must be at most 100 characters',
      futureDate: 'Date cannot be in the future',
      invalidGender: 'Gender must be either male or female',
      invalidSchoolId: 'School ID must be a positive integer'
    }
  },

  // Errors
  errors: {
    network: 'Network error. Please check your connection.',
    unauthorized: 'You are not authorized to perform this action.',
    server: 'Server error. Please try again later.',
    notFound: 'The requested resource was not found.',
    validation: 'Please check your input and try again.',
    generic: 'An unexpected error occurred.'
  },

  // Success messages
  success: {
    saved: 'Changes saved successfully',
    deleted: 'Item deleted successfully',
    updated: 'Updated successfully',
    created: 'Created successfully'
  },

  // Time and dates
  time: {
    now: 'now',
    today: 'today',
    yesterday: 'yesterday',
    tomorrow: 'tomorrow',
    thisWeek: 'this week',
    lastWeek: 'last week',
    thisMonth: 'this month',
    lastMonth: 'last month'
  },

  // Gender options
  gender: {
    male: 'Male',
    female: 'Female',
    other: 'Other',
    preferNotToSay: 'Prefer not to say'
  },

  // PWA
  pwa: {
    installPrompt: 'Install App',
    updateAvailable: 'A new version is available. Click to update.',
    offline: 'You are now offline. Some features may be limited.',
    online: 'Connection restored'
  },

  // Accessibility
  a11y: {
    skipToMain: 'Skip to main content',
    closeDialog: 'Close dialog',
    openMenu: 'Open menu',
    closeMenu: 'Close menu',
    loading: 'Loading content',
    error: 'Error occurred'
  }
};
