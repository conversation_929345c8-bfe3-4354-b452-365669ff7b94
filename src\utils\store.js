export class Store {
  constructor(initialState = {}) {
    this.state = { ...initialState };
    this.listeners = [];
    this.middleware = [];
  }

  // Get current state
  getState() {
    return { ...this.state };
  }

  // Subscribe to state changes
  subscribe(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Dispatch action to update state
  dispatch(action) {
    // Apply middleware
    let processedAction = action;
    for (const middleware of this.middleware) {
      processedAction = middleware(processedAction, this.getState(), this.dispatch.bind(this));
    }

    // Update state
    const previousState = { ...this.state };
    this.state = this.reducer(this.state, processedAction);

    // Notify listeners
    this.listeners.forEach(listener => {
      listener(this.state, previousState, processedAction);
    });
  }

  // Default reducer (can be overridden)
  reducer(state, action) {
    switch (action.type) {
      case 'SET_STATE':
        return { ...state, ...action.payload };
      case 'RESET_STATE':
        return action.payload || {};
      default:
        return state;
    }
  }

  // Add middleware
  use(middleware) {
    this.middleware.push(middleware);
  }

  // Helper methods
  setState(updates) {
    this.dispatch({ type: 'SET_STATE', payload: updates });
  }

  resetState(newState = {}) {
    this.dispatch({ type: 'RESET_STATE', payload: newState });
  }
}

// Global store instance
export const globalStore = new Store({
  user: null,
  isAuthenticated: false,
  theme: 'light',
  language: 'en',
  loading: false,
  error: null
});

// Action creators
export const actions = {
  setUser: (user) => ({ type: 'SET_USER', payload: user }),
  clearUser: () => ({ type: 'CLEAR_USER' }),
  setLoading: (loading) => ({ type: 'SET_LOADING', payload: loading }),
  setError: (error) => ({ type: 'SET_ERROR', payload: error }),
  clearError: () => ({ type: 'CLEAR_ERROR' }),
  setTheme: (theme) => ({ type: 'SET_THEME', payload: theme }),
  setLanguage: (language) => ({ type: 'SET_LANGUAGE', payload: language })
};

// Enhanced reducer for global store
globalStore.reducer = (state, action) => {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload
      };
    
    case 'CLEAR_USER':
      return {
        ...state,
        user: null,
        isAuthenticated: false
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      };
    
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };
    
    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload
      };
    
    case 'SET_LANGUAGE':
      return {
        ...state,
        language: action.payload
      };
    
    default:
      return Store.prototype.reducer.call(this, state, action);
  }
};

// Middleware
export const loggerMiddleware = (action, state, dispatch) => {
  console.log('Action:', action);
  console.log('Previous State:', state);
  return action;
};

export const persistMiddleware = (action, state, dispatch) => {
  // Persist certain state to localStorage
  const persistKeys = ['theme', 'language', 'user'];
  
  setTimeout(() => {
    const newState = globalStore.getState();
    persistKeys.forEach(key => {
      if (newState[key] !== undefined) {
        localStorage.setItem(`store_${key}`, JSON.stringify(newState[key]));
      }
    });
  }, 0);
  
  return action;
};

export const asyncMiddleware = (action, state, dispatch) => {
  // Handle async actions
  if (typeof action === 'function') {
    return action(dispatch, () => globalStore.getState());
  }
  return action;
};

// Initialize middleware
if (process.env.NODE_ENV === 'development') {
  globalStore.use(loggerMiddleware);
}
globalStore.use(persistMiddleware);
globalStore.use(asyncMiddleware);

// Restore persisted state
const restorePersistedState = () => {
  const persistKeys = ['theme', 'language', 'user'];
  const restoredState = {};
  
  persistKeys.forEach(key => {
    const stored = localStorage.getItem(`store_${key}`);
    if (stored) {
      try {
        restoredState[key] = JSON.parse(stored);
      } catch (error) {
        console.warn(`Failed to restore ${key} from localStorage:`, error);
      }
    }
  });
  
  if (Object.keys(restoredState).length > 0) {
    globalStore.setState(restoredState);
  }
};

// Selectors
export const selectors = {
  getUser: (state) => state.user,
  isAuthenticated: (state) => state.isAuthenticated,
  getTheme: (state) => state.theme,
  getLanguage: (state) => state.language,
  isLoading: (state) => state.loading,
  getError: (state) => state.error
};

// React-like hooks for components
export const useStore = (selector) => {
  const [state, setState] = useState(selector ? selector(globalStore.getState()) : globalStore.getState());
  
  useEffect(() => {
    const unsubscribe = globalStore.subscribe((newState) => {
      const selectedState = selector ? selector(newState) : newState;
      setState(selectedState);
    });
    
    return unsubscribe;
  }, [selector]);
  
  return state;
};

// Simple useState and useEffect implementations for vanilla JS
const useState = (initialValue) => {
  let value = initialValue;
  const listeners = [];
  
  const setValue = (newValue) => {
    value = newValue;
    listeners.forEach(listener => listener(value));
  };
  
  const subscribe = (listener) => {
    listeners.push(listener);
    return () => {
      const index = listeners.indexOf(listener);
      if (index > -1) listeners.splice(index, 1);
    };
  };
  
  return [value, setValue, subscribe];
};

const useEffect = (effect, deps) => {
  // Simple implementation - in real React this would be more sophisticated
  effect();
};

// Async action creators
export const asyncActions = {
  login: (credentials) => async (dispatch, getState) => {
    dispatch(actions.setLoading(true));
    dispatch(actions.clearError());
    
    try {
      // This would be replaced with actual API call
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      });
      
      if (!response.ok) {
        throw new Error('Login failed');
      }
      
      const data = await response.json();
      dispatch(actions.setUser(data.user));
    } catch (error) {
      dispatch(actions.setError(error.message));
    } finally {
      dispatch(actions.setLoading(false));
    }
  },
  
  logout: () => async (dispatch) => {
    dispatch(actions.setLoading(true));
    
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      dispatch(actions.clearUser());
      dispatch(actions.setLoading(false));
    }
  }
};

// Initialize store
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    restorePersistedState();
  });
}
