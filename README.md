# PetaTalenta Frontend

A modular frontend application built with Vanilla JavaScript and Tailwind CSS for the PetaTalenta platform.

## Features

- **Authentication System**: Login, Register, Profile Management
- **Modular Architecture**: Clean separation of concerns with components, services, and utilities
- **Responsive Design**: Built with Tailwind CSS for mobile-first responsive design
- **Client-side Routing**: SPA routing without external dependencies
- **Form Validation**: Client-side validation with user-friendly error messages
- **Token-based Authentication**: JWT token management with localStorage

## Project Structure

```
src/
├── components/
│   ├── auth/
│   │   ├── LoginForm.js      # Login form component
│   │   └── RegisterForm.js   # Registration form component
│   └── layout/
│       └── Navbar.js         # Navigation bar component
├── config/
│   └── api.js               # API configuration and HTTP client
├── pages/
│   ├── Dashboard.js         # Dashboard page
│   └── Profile.js           # User profile page
├── router/
│   └── Router.js            # Client-side router
├── services/
│   └── authService.js       # Authentication service
├── utils/
│   └── auth.js              # Authentication utilities and validators
├── main.js                  # Application entry point
└── style.css                # Global styles with Tailwind
```

## API Integration

The application integrates with the PetaTalenta API at `https://api.chhrone.web.id` with the following endpoints:

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/logout` - User logout
- `DELETE /api/auth/account` - Delete user account

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd petatalenta-fe
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## Usage

### Authentication Flow

1. **Registration**: Users can create new accounts with email and password
2. **Login**: Existing users can log in with their credentials
3. **Dashboard**: Authenticated users are redirected to the dashboard
4. **Profile Management**: Users can update their profile information and change passwords

### Navigation

The application uses client-side routing with the following routes:

- `/` - Redirects to dashboard (requires authentication)
- `/login` - Login page
- `/register` - Registration page
- `/dashboard` - User dashboard (requires authentication)
- `/profile` - User profile page (requires authentication)

### Form Validation

All forms include client-side validation with the following rules:

#### Registration/Login
- **Email**: Valid email format, required
- **Password**: Minimum 8 characters, at least one letter and one number

#### Profile Update
- **Username**: Alphanumeric only, 3-100 characters (optional)
- **Full Name**: Maximum 100 characters (optional)
- **Date of Birth**: Valid date, cannot be in the future (optional)
- **Gender**: Must be "male" or "female" (optional)
- **School ID**: Positive integer (optional)

## Components

### LoginForm
Handles user authentication with email and password validation.

### RegisterForm
Manages user registration with password confirmation and validation.

### Dashboard
Displays user information and account statistics.

### Profile
Comprehensive profile management including:
- Personal information updates
- Password changes
- Account deletion

### Navbar
Navigation component that adapts based on authentication status.

## Services

### AuthService
Centralized authentication service handling:
- User registration and login
- Profile management
- Password changes
- Account deletion
- Token management

### ApiClient
HTTP client with automatic token injection and error handling.

## Utilities

### AuthManager
Local storage management for user data and authentication tokens.

### Validator
Form validation utilities for common validation patterns.

### Router
Client-side routing utilities for navigation.

## Styling

The application uses Tailwind CSS with custom utility classes:

- `.btn-primary` - Primary button styling
- `.btn-secondary` - Secondary button styling
- `.input-field` - Form input styling
- `.card` - Card container styling
- `.error-message` - Error message styling
- `.success-message` - Success message styling

## Development

### Adding New Pages

1. Create a new component in `src/pages/`
2. Add the route to `src/router/Router.js`
3. Import and configure the component

### Adding New API Endpoints

1. Add endpoint configuration to `src/config/api.js`
2. Create or update service methods in `src/services/`
3. Use the service in your components

### Custom Styling

Add custom styles to `src/style.css` using Tailwind's `@apply` directive or standard CSS.

## Testing

### Manual Testing

You can run manual tests in the browser console:

```javascript
// Import and run auth tests
import('./src/tests/auth.test.js').then(module => {
  module.AuthTests.runAllTests();
});
```

### API Testing

Use the browser's Network tab to monitor API calls, or use tools like:
- Postman
- Insomnia
- curl

See `docs/API.md` for detailed API documentation.

## Deployment

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration.

## Troubleshooting

### Common Issues

1. **API Connection Issues**
   - Check if the API base URL is correct in your environment variables
   - Verify network connectivity
   - Check browser console for CORS errors

2. **Authentication Issues**
   - Clear localStorage: `localStorage.clear()`
   - Check if tokens are expired
   - Verify API credentials

3. **Build Issues**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Clear Vite cache: `npx vite --force`

### Debug Mode

Enable debug logging by setting `VITE_ENABLE_LOGGING=true` in your `.env` file.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Test your changes
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Advanced Features

### Progressive Web App (PWA)
- Service Worker for offline functionality
- App installation prompt
- Push notifications support
- Background sync capabilities

### Internationalization (i18n)
- Multi-language support (English, Indonesian)
- Automatic language detection
- RTL language support
- Date/time/number formatting

### Performance Optimizations
- Lazy loading utilities
- Virtual scrolling for large lists
- Bundle splitting and code optimization
- Performance monitoring tools

### Security Features
- XSS prevention utilities
- CSRF protection
- Input validation and sanitization
- Secure token management

### Accessibility
- ARIA attributes management
- Focus trap utilities
- Screen reader support
- Keyboard navigation helpers

### State Management
- Simple Redux-like store
- Middleware support
- Persistent state management
- Async action creators

### Testing Utilities
- Built-in testing framework
- Mock functions and utilities
- DOM testing helpers
- Performance benchmarking

## Scripts

### Development
```bash
npm run dev          # Start development server
npm run preview      # Preview production build
```

### Building
```bash
npm run build                # Production build
npm run build:staging        # Staging build
npm run build:production     # Production build with optimizations
npm run build-custom         # Custom build with additional features
npm run analyze             # Build with bundle analysis
```

### Deployment
```bash
npm run deploy:staging       # Deploy to staging
npm run deploy:production    # Deploy to production
```

### Testing and Quality
```bash
npm run test        # Run tests
npm run lint        # Run linting
```

## Architecture

### Modular Structure
The application follows a modular architecture with clear separation of concerns:

- **Components**: Reusable UI components
- **Pages**: Route-specific page components
- **Services**: API and business logic
- **Utils**: Utility functions and helpers
- **Config**: Configuration and environment settings

### Data Flow
1. User interactions trigger events
2. Events dispatch actions to services
3. Services communicate with APIs
4. State updates trigger UI re-renders
5. Components reflect new state

### Error Handling
- Global error boundaries
- API error handling
- User-friendly error messages
- Error logging and reporting

## Customization

### Theming
The application supports custom themes through Tailwind CSS:

```css
/* Add custom theme colors */
:root {
  --primary-color: #your-color;
  --secondary-color: #your-color;
}
```

### Adding New Languages
1. Create a new locale file in `src/locales/`
2. Import and register the locale
3. Add language option to selectors

### Custom Components
Follow the existing component structure:

```javascript
export class MyComponent {
  constructor(container) {
    this.container = container;
    this.render();
    this.attachEventListeners();
  }

  render() {
    // Component HTML
  }

  attachEventListeners() {
    // Event handling
  }
}
```

## Performance

### Optimization Techniques
- Code splitting and lazy loading
- Image optimization and lazy loading
- Service Worker caching
- Bundle size optimization
- Performance monitoring

### Monitoring
The application includes built-in performance monitoring:
- Page load times
- API response times
- User interaction metrics
- Error tracking

## Security

### Best Practices Implemented
- Input validation and sanitization
- XSS prevention
- CSRF protection
- Secure token storage
- Content Security Policy headers

### Security Checklist
- [ ] Enable HTTPS in production
- [ ] Configure CSP headers
- [ ] Implement rate limiting
- [ ] Regular security audits
- [ ] Keep dependencies updated

## License

This project is licensed under the MIT License.
