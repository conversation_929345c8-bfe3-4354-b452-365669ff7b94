// Environment configuration
export const ENV = {
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://api.chhrone.web.id',
  
  // App Configuration
  APP_NAME: import.meta.env.VITE_APP_NAME || 'PetaTalenta',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // Development flags
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
  
  // Debug settings
  ENABLE_LOGGING: import.meta.env.VITE_ENABLE_LOGGING === 'true' || import.meta.env.DEV,
  
  // Storage keys
  STORAGE_KEYS: {
    TOKEN: 'petatalenta_token',
    USER: 'petatalenta_user',
    THEME: 'petatalenta_theme'
  }
};

// Logger utility
export class Logger {
  static log(...args) {
    if (ENV.ENABLE_LOGGING) {
      console.log('[PetaTalenta]', ...args);
    }
  }

  static error(...args) {
    if (ENV.ENABLE_LOGGING) {
      console.error('[PetaTalenta Error]', ...args);
    }
  }

  static warn(...args) {
    if (ENV.ENABLE_LOGGING) {
      console.warn('[PetaTalenta Warning]', ...args);
    }
  }

  static info(...args) {
    if (ENV.ENABLE_LOGGING) {
      console.info('[PetaTalenta Info]', ...args);
    }
  }
}
