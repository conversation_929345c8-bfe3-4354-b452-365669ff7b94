import { ENV } from '../config/env.js';
import { VALIDATION_RULES } from '../constants/index.js';

// Authentication utilities
export class AuthManager {
  static TOKEN_KEY = ENV.STORAGE_KEYS.TOKEN;
  static USER_KEY = ENV.STORAGE_KEYS.USER;

  static setToken(token) {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static getToken() {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static removeToken() {
    localStorage.removeItem(this.TOKEN_KEY);
  }

  static setUser(user) {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  static getUser() {
    const user = localStorage.getItem(this.USER_KEY);
    return user ? JSON.parse(user) : null;
  }

  static removeUser() {
    localStorage.removeItem(this.USER_KEY);
  }

  static isAuthenticated() {
    return !!this.getToken();
  }

  static logout() {
    this.removeToken();
    this.removeUser();
  }

  static login(token, user) {
    this.setToken(token);
    this.setUser(user);
  }
}

// Form validation utilities
export class Validator {
  static validateEmail(email) {
    return VALIDATION_RULES.EMAIL.PATTERN.test(email) && email.length <= VALIDATION_RULES.EMAIL.MAX_LENGTH;
  }

  static validatePassword(password) {
    return VALIDATION_RULES.PASSWORD.PATTERN.test(password);
  }

  static validateRequired(value) {
    return value && value.trim().length > 0;
  }

  static validateUsername(username) {
    return VALIDATION_RULES.USERNAME.PATTERN.test(username);
  }
}

// Router utilities
export class Router {
  static navigate(path) {
    window.history.pushState({}, '', path);
    window.dispatchEvent(new PopStateEvent('popstate'));
  }

  static getCurrentPath() {
    return window.location.pathname;
  }

  static onRouteChange(callback) {
    window.addEventListener('popstate', callback);
    // Also listen for initial load
    window.addEventListener('DOMContentLoaded', callback);
  }
}
