# Deployment Guide

This guide covers various deployment options for the PetaTalenta Frontend application.

## Prerequisites

- Node.js 16+ installed
- npm or yarn package manager
- Git for version control
- Access to your chosen hosting platform

## Environment Variables

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Configure the following variables:

```env
# API Configuration
VITE_API_BASE_URL=https://api.chhrone.web.id

# App Configuration
VITE_APP_NAME=PetaTalenta
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_ENABLE_LOGGING=false
```

## Build Process

### Development Build
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Staging Build
```bash
npm run build:staging
```

## Deployment Options

### 1. Netlify

#### Automatic Deployment (Recommended)

1. Connect your GitHub repository to Netlify
2. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
3. Set environment variables in Netlify dashboard
4. Deploy automatically on git push

#### Manual Deployment

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy
netlify deploy --prod --dir=dist
```

#### Using Deploy Script

```bash
# Set environment variables
export NETLIFY_AUTH_TOKEN=your_token
export NETLIFY_SITE_ID=your_site_id

# Deploy
npm run deploy:production
```

### 2. Vercel

#### Automatic Deployment

1. Connect your GitHub repository to Vercel
2. Configure build settings (auto-detected)
3. Set environment variables in Vercel dashboard
4. Deploy automatically on git push

#### Manual Deployment

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

#### Using Deploy Script

```bash
npm run deploy:production
```

### 3. GitHub Pages

#### Setup

1. Enable GitHub Pages in repository settings
2. Choose source: GitHub Actions

#### Deploy

```bash
# Install gh-pages
npm install -g gh-pages

# Deploy
npm run build
npx gh-pages -d dist
```

#### GitHub Actions Workflow

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
      env:
        VITE_API_BASE_URL: ${{ secrets.VITE_API_BASE_URL }}
    
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

### 4. AWS S3 + CloudFront

#### Setup

1. Create S3 bucket for static website hosting
2. Create CloudFront distribution
3. Configure Route 53 (optional)

#### Deploy

```bash
# Install AWS CLI
pip install awscli

# Configure AWS credentials
aws configure

# Set environment variables
export S3_BUCKET=your-bucket-name
export CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id

# Deploy
npm run deploy:production
```

### 5. Traditional Web Hosting (FTP/SFTP)

#### Build and Upload

```bash
# Build the application
npm run build

# Upload dist/ folder contents to your web server
# Using FTP client or command line tools
```

#### Using Deploy Script

```bash
# Set FTP credentials
export FTP_HOST=your-host
export FTP_USER=your-username
export FTP_PASSWORD=your-password
export FTP_REMOTE_PATH=/public_html

# Deploy
npm run deploy:production
```

## Custom Deployment

### Docker Deployment

Create `Dockerfile`:

```dockerfile
FROM nginx:alpine

# Copy built application
COPY dist/ /usr/share/nginx/html/

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

Build and run:

```bash
# Build Docker image
docker build -t petatalenta-fe .

# Run container
docker run -p 80:80 petatalenta-fe
```

## Post-Deployment

### Verification Checklist

- [ ] Application loads correctly
- [ ] All routes work properly
- [ ] API integration functions
- [ ] Authentication flow works
- [ ] Responsive design displays correctly
- [ ] PWA features work (if enabled)
- [ ] Performance is acceptable

### Monitoring

1. Set up error tracking (Sentry, LogRocket)
2. Configure analytics (Google Analytics, Mixpanel)
3. Monitor performance (Lighthouse, WebPageTest)
4. Set up uptime monitoring

### SSL/HTTPS

Ensure your deployment includes SSL certificate:

- Netlify/Vercel: Automatic HTTPS
- CloudFront: Configure SSL certificate
- Traditional hosting: Install SSL certificate

## Troubleshooting

### Common Issues

1. **Build Fails**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check for syntax errors

2. **Routes Don't Work**
   - Configure server for SPA routing
   - Check base URL configuration
   - Verify router setup

3. **API Calls Fail**
   - Check CORS configuration
   - Verify API base URL
   - Check authentication headers

4. **Assets Not Loading**
   - Check public path configuration
   - Verify asset paths
   - Check server configuration

### Debug Mode

Enable debug logging:

```env
VITE_ENABLE_LOGGING=true
```

### Performance Issues

1. Enable gzip compression
2. Configure caching headers
3. Optimize images
4. Use CDN for static assets

## Rollback Strategy

### Quick Rollback

1. Keep previous build artifacts
2. Use deployment platform rollback features
3. Maintain deployment history

### Manual Rollback

```bash
# Revert to previous commit
git revert HEAD

# Rebuild and deploy
npm run build
npm run deploy:production
```

## Security Considerations

1. **Environment Variables**
   - Never commit sensitive data
   - Use platform-specific secret management
   - Rotate keys regularly

2. **Content Security Policy**
   - Configure CSP headers
   - Restrict resource loading
   - Monitor CSP violations

3. **HTTPS**
   - Force HTTPS redirects
   - Use HSTS headers
   - Configure secure cookies

For more detailed information, refer to your hosting platform's documentation.
