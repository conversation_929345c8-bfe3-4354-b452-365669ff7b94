// Application constants

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile'
};

export const USER_TYPES = {
  USER: 'user',
  ADMIN: 'admin',
  MODERATOR: 'moderator'
};

export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' }
];

export const VALIDATION_RULES = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MAX_LENGTH: 255
  },
  PASSWORD: {
    PATTERN: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/,
    MIN_LENGTH: 8
  },
  USERNAME: {
    PATTERN: /^[a-zA-Z0-9]{3,100}$/,
    MIN_LENGTH: 3,
    MAX_LENGTH: 100
  },
  FULL_NAME: {
    MAX_LENGTH: 100
  }
};

export const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PASSWORD: 'Password must be at least 8 characters with at least one letter and one number',
  INVALID_USERNAME: 'Username must be 3-100 characters and alphanumeric only',
  PASSWORD_MISMATCH: 'Passwords do not match',
  FULL_NAME_TOO_LONG: 'Full name must be at most 100 characters',
  FUTURE_DATE: 'Date cannot be in the future',
  INVALID_GENDER: 'Gender must be either male or female',
  INVALID_SCHOOL_ID: 'School ID must be a positive integer',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  SERVER_ERROR: 'Server error. Please try again later.'
};

export const SUCCESS_MESSAGES = {
  LOGIN: 'Login successful! Redirecting...',
  REGISTER: 'Account created successfully! Redirecting...',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
  ACCOUNT_DELETED: 'Account deleted successfully'
};

export const LOADING_MESSAGES = {
  SIGNING_IN: 'Signing in...',
  CREATING_ACCOUNT: 'Creating Account...',
  LOADING_PROFILE: 'Loading profile...',
  SAVING: 'Saving...',
  CHANGING_PASSWORD: 'Changing...',
  DELETING_ACCOUNT: 'Deleting account...'
};

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500
};
