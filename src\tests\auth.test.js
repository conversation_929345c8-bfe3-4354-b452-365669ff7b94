// Simple test utilities for manual testing
import { Validator, AuthManager } from '../utils/auth.js';

export class AuthTests {
  static runAllTests() {
    console.log('Running Auth Tests...');
    
    this.testEmailValidation();
    this.testPasswordValidation();
    this.testUsernameValidation();
    this.testAuthManager();
    
    console.log('All tests completed!');
  }

  static testEmailValidation() {
    console.log('Testing email validation...');
    
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    const invalidEmails = [
      'invalid-email',
      '@domain.com',
      'user@',
      'user@domain',
      ''
    ];
    
    validEmails.forEach(email => {
      if (!Validator.validateEmail(email)) {
        console.error(`Valid email failed: ${email}`);
      }
    });
    
    invalidEmails.forEach(email => {
      if (Validator.validateEmail(email)) {
        console.error(`Invalid email passed: ${email}`);
      }
    });
    
    console.log('Email validation tests completed');
  }

  static testPasswordValidation() {
    console.log('Testing password validation...');
    
    const validPasswords = [
      'password123',
      'MyPass1',
      'test1234',
      'SecureP@ss1'
    ];
    
    const invalidPasswords = [
      'short',
      'password',
      '12345678',
      'PASSWORD123',
      ''
    ];
    
    validPasswords.forEach(password => {
      if (!Validator.validatePassword(password)) {
        console.error(`Valid password failed: ${password}`);
      }
    });
    
    invalidPasswords.forEach(password => {
      if (Validator.validatePassword(password)) {
        console.error(`Invalid password passed: ${password}`);
      }
    });
    
    console.log('Password validation tests completed');
  }

  static testUsernameValidation() {
    console.log('Testing username validation...');
    
    const validUsernames = [
      'user123',
      'testuser',
      'User1',
      'abc'
    ];
    
    const invalidUsernames = [
      'us',
      'user-name',
      'user@name',
      'user name',
      '',
      'a'.repeat(101)
    ];
    
    validUsernames.forEach(username => {
      if (!Validator.validateUsername(username)) {
        console.error(`Valid username failed: ${username}`);
      }
    });
    
    invalidUsernames.forEach(username => {
      if (Validator.validateUsername(username)) {
        console.error(`Invalid username passed: ${username}`);
      }
    });
    
    console.log('Username validation tests completed');
  }

  static testAuthManager() {
    console.log('Testing AuthManager...');
    
    // Test token management
    const testToken = 'test-token-123';
    AuthManager.setToken(testToken);
    
    if (AuthManager.getToken() !== testToken) {
      console.error('Token storage failed');
    }
    
    if (!AuthManager.isAuthenticated()) {
      console.error('Authentication check failed');
    }
    
    // Test user management
    const testUser = { id: 1, email: '<EMAIL>' };
    AuthManager.setUser(testUser);
    
    const retrievedUser = AuthManager.getUser();
    if (!retrievedUser || retrievedUser.email !== testUser.email) {
      console.error('User storage failed');
    }
    
    // Test logout
    AuthManager.logout();
    
    if (AuthManager.isAuthenticated()) {
      console.error('Logout failed');
    }
    
    if (AuthManager.getUser() !== null) {
      console.error('User cleanup failed');
    }
    
    console.log('AuthManager tests completed');
  }
}

// Export for manual testing in browser console
window.AuthTests = AuthTests;
