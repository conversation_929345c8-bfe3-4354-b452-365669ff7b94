import { AuthService } from '../../services/authService.js';
import { Validator, Router } from '../../utils/auth.js';
import { Toast } from '../common/Toast.js';

export class LoginForm {
  constructor(container) {
    this.container = container;
    this.render();
    this.attachEventListeners();
  }

  render() {
    this.container.innerHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
          <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Sign in to your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              Or
              <a href="#" id="switch-to-register" class="font-medium text-blue-600 hover:text-blue-500">
                create a new account
              </a>
            </p>
          </div>
          <form class="mt-8 space-y-6" id="login-form">
            <div class="rounded-md shadow-sm -space-y-px">
              <div>
                <label for="email" class="sr-only">Email address</label>
                <input 
                  id="email" 
                  name="email" 
                  type="email" 
                  autocomplete="email" 
                  required 
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                  placeholder="Email address"
                >
                <div id="email-error" class="error-message hidden"></div>
              </div>
              <div>
                <label for="password" class="sr-only">Password</label>
                <input 
                  id="password" 
                  name="password" 
                  type="password" 
                  autocomplete="current-password" 
                  required 
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                  placeholder="Password"
                >
                <div id="password-error" class="error-message hidden"></div>
              </div>
            </div>

            <div id="form-error" class="error-message hidden text-center"></div>
            <div id="form-success" class="success-message hidden text-center"></div>

            <div>
              <button 
                type="submit" 
                id="login-btn"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span id="login-text">Sign in</span>
                <span id="login-loading" class="hidden">Signing in...</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    const form = this.container.querySelector('#login-form');
    const switchToRegister = this.container.querySelector('#switch-to-register');

    form.addEventListener('submit', this.handleSubmit.bind(this));
    switchToRegister.addEventListener('click', (e) => {
      e.preventDefault();
      Router.navigate('/register');
    });
  }

  async handleSubmit(e) {
    e.preventDefault();
    
    const email = this.container.querySelector('#email').value;
    const password = this.container.querySelector('#password').value;
    
    // Clear previous errors
    this.clearErrors();
    
    // Validate form
    if (!this.validateForm(email, password)) {
      return;
    }

    // Show loading state
    this.setLoading(true);

    try {
      await AuthService.login(email, password);
      Toast.success('Login successful! Redirecting...');

      setTimeout(() => {
        Router.navigate('/dashboard');
      }, 1000);
    } catch (error) {
      this.showError(error.message);
      Toast.error(error.message);
    } finally {
      this.setLoading(false);
    }
  }

  validateForm(email, password) {
    let isValid = true;

    if (!Validator.validateRequired(email)) {
      this.showFieldError('email', 'Email is required');
      isValid = false;
    } else if (!Validator.validateEmail(email)) {
      this.showFieldError('email', 'Please enter a valid email address');
      isValid = false;
    }

    if (!Validator.validateRequired(password)) {
      this.showFieldError('password', 'Password is required');
      isValid = false;
    }

    return isValid;
  }

  showFieldError(fieldName, message) {
    const errorElement = this.container.querySelector(`#${fieldName}-error`);
    errorElement.textContent = message;
    errorElement.classList.remove('hidden');
  }

  showError(message) {
    const errorElement = this.container.querySelector('#form-error');
    errorElement.textContent = message;
    errorElement.classList.remove('hidden');
  }

  showSuccess(message) {
    const successElement = this.container.querySelector('#form-success');
    successElement.textContent = message;
    successElement.classList.remove('hidden');
  }

  clearErrors() {
    const errorElements = this.container.querySelectorAll('.error-message, .success-message');
    errorElements.forEach(element => {
      element.classList.add('hidden');
      element.textContent = '';
    });
  }

  setLoading(loading) {
    const btn = this.container.querySelector('#login-btn');
    const text = this.container.querySelector('#login-text');
    const loadingText = this.container.querySelector('#login-loading');

    btn.disabled = loading;
    
    if (loading) {
      text.classList.add('hidden');
      loadingText.classList.remove('hidden');
    } else {
      text.classList.remove('hidden');
      loadingText.classList.add('hidden');
    }
  }
}
