export class PerformanceUtils {
  // Lazy loading utilities
  static setupLazyLoading(selector = 'img[data-lazy]') {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.lazy;
            img.classList.remove('lazy');
            observer.unobserve(img);
          }
        });
      });

      document.querySelectorAll(selector).forEach(img => {
        imageObserver.observe(img);
      });
    } else {
      // Fallback for older browsers
      document.querySelectorAll(selector).forEach(img => {
        img.src = img.dataset.lazy;
      });
    }
  }

  // Virtual scrolling for large lists
  static createVirtualList(container, items, itemHeight, renderItem) {
    const containerHeight = container.clientHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2;
    let scrollTop = 0;
    let startIndex = 0;

    const totalHeight = items.length * itemHeight;
    const viewport = document.createElement('div');
    viewport.style.height = `${totalHeight}px`;
    viewport.style.position = 'relative';

    const renderVisibleItems = () => {
      startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(startIndex + visibleCount, items.length);

      // Clear existing items
      viewport.innerHTML = '';

      // Render visible items
      for (let i = startIndex; i < endIndex; i++) {
        const item = renderItem(items[i], i);
        item.style.position = 'absolute';
        item.style.top = `${i * itemHeight}px`;
        item.style.height = `${itemHeight}px`;
        viewport.appendChild(item);
      }
    };

    container.addEventListener('scroll', () => {
      scrollTop = container.scrollTop;
      renderVisibleItems();
    });

    container.appendChild(viewport);
    renderVisibleItems();

    return {
      update: (newItems) => {
        items = newItems;
        viewport.style.height = `${items.length * itemHeight}px`;
        renderVisibleItems();
      }
    };
  }

  // Debounce and throttle utilities
  static debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func(...args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func(...args);
    };
  }

  static throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Memory management
  static createObjectPool(createFn, resetFn, initialSize = 10) {
    const pool = [];
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      pool.push(createFn());
    }

    return {
      get() {
        return pool.length > 0 ? pool.pop() : createFn();
      },
      
      release(obj) {
        if (resetFn) resetFn(obj);
        pool.push(obj);
      },
      
      size() {
        return pool.length;
      }
    };
  }

  // Performance monitoring
  static measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  static async measureAsyncPerformance(name, asyncFn) {
    const start = performance.now();
    const result = await asyncFn();
    const end = performance.now();
    
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  // Resource loading optimization
  static preloadResource(url, type = 'fetch') {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    switch (type) {
      case 'image':
        link.as = 'image';
        break;
      case 'script':
        link.as = 'script';
        break;
      case 'style':
        link.as = 'style';
        break;
      default:
        link.as = 'fetch';
        link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  }

  static prefetchResource(url) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = url;
    document.head.appendChild(link);
  }

  // Bundle splitting utilities
  static async loadModule(modulePath) {
    try {
      const module = await import(modulePath);
      return module;
    } catch (error) {
      console.error(`Failed to load module: ${modulePath}`, error);
      throw error;
    }
  }

  // Critical resource loading
  static loadCriticalCSS(css) {
    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);
  }

  static async loadNonCriticalCSS(href) {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.onload = resolve;
      link.onerror = reject;
      document.head.appendChild(link);
    });
  }

  // Web Workers utilities
  static createWorker(workerFunction) {
    const blob = new Blob([`(${workerFunction.toString()})()`], {
      type: 'application/javascript'
    });
    return new Worker(URL.createObjectURL(blob));
  }

  // Service Worker utilities
  static async registerServiceWorker(swPath) {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register(swPath);
        console.log('Service Worker registered:', registration);
        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        throw error;
      }
    } else {
      console.warn('Service Workers not supported');
    }
  }

  // Cache utilities
  static createCache(maxSize = 100) {
    const cache = new Map();
    
    return {
      get(key) {
        if (cache.has(key)) {
          // Move to end (most recently used)
          const value = cache.get(key);
          cache.delete(key);
          cache.set(key, value);
          return value;
        }
        return null;
      },
      
      set(key, value) {
        if (cache.has(key)) {
          cache.delete(key);
        } else if (cache.size >= maxSize) {
          // Remove least recently used
          const firstKey = cache.keys().next().value;
          cache.delete(firstKey);
        }
        cache.set(key, value);
      },
      
      has(key) {
        return cache.has(key);
      },
      
      delete(key) {
        return cache.delete(key);
      },
      
      clear() {
        cache.clear();
      },
      
      size() {
        return cache.size;
      }
    };
  }

  // Performance observer
  static observePerformance(callback) {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
      return observer;
    }
  }

  // Frame rate monitoring
  static monitorFrameRate(callback, duration = 1000) {
    let frames = 0;
    let lastTime = performance.now();
    
    const countFrame = (currentTime) => {
      frames++;
      
      if (currentTime - lastTime >= duration) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime));
        callback(fps);
        frames = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFrame);
    };
    
    requestAnimationFrame(countFrame);
  }
}
