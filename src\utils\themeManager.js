import { ENV } from '../config/env.js';

export class ThemeManager {
  static THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
    SYSTEM: 'system'
  };

  static currentTheme = null;

  static init() {
    this.currentTheme = this.getStoredTheme() || this.THEMES.SYSTEM;
    this.applyTheme(this.currentTheme);
    this.setupSystemThemeListener();
  }

  static getStoredTheme() {
    return localStorage.getItem(ENV.STORAGE_KEYS.THEME);
  }

  static setStoredTheme(theme) {
    localStorage.setItem(ENV.STORAGE_KEYS.THEME, theme);
  }

  static getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches 
      ? this.THEMES.DARK 
      : this.THEMES.LIGHT;
  }

  static applyTheme(theme) {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    
    let effectiveTheme = theme;
    
    if (theme === this.THEMES.SYSTEM) {
      effectiveTheme = this.getSystemTheme();
    }
    
    // Apply theme class
    root.classList.add(effectiveTheme);
    
    // Update meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', 
        effectiveTheme === this.THEMES.DARK ? '#1f2937' : '#2563eb'
      );
    }
    
    this.currentTheme = theme;
    this.setStoredTheme(theme);
  }

  static setTheme(theme) {
    if (Object.values(this.THEMES).includes(theme)) {
      this.applyTheme(theme);
      this.notifyThemeChange(theme);
    }
  }

  static toggleTheme() {
    const currentEffectiveTheme = this.getCurrentEffectiveTheme();
    const newTheme = currentEffectiveTheme === this.THEMES.DARK 
      ? this.THEMES.LIGHT 
      : this.THEMES.DARK;
    
    this.setTheme(newTheme);
  }

  static getCurrentTheme() {
    return this.currentTheme;
  }

  static getCurrentEffectiveTheme() {
    if (this.currentTheme === this.THEMES.SYSTEM) {
      return this.getSystemTheme();
    }
    return this.currentTheme;
  }

  static setupSystemThemeListener() {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    mediaQuery.addEventListener('change', () => {
      if (this.currentTheme === this.THEMES.SYSTEM) {
        this.applyTheme(this.THEMES.SYSTEM);
        this.notifyThemeChange(this.THEMES.SYSTEM);
      }
    });
  }

  static notifyThemeChange(theme) {
    const event = new CustomEvent('themechange', {
      detail: {
        theme,
        effectiveTheme: this.getCurrentEffectiveTheme()
      }
    });
    
    window.dispatchEvent(event);
  }

  static onThemeChange(callback) {
    window.addEventListener('themechange', callback);
  }

  static offThemeChange(callback) {
    window.removeEventListener('themechange', callback);
  }

  // Theme toggle component
  static createThemeToggle() {
    const toggle = document.createElement('button');
    toggle.className = 'theme-toggle p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors';
    toggle.setAttribute('aria-label', 'Toggle theme');
    
    this.updateToggleIcon(toggle);
    
    toggle.addEventListener('click', () => {
      this.toggleTheme();
    });
    
    this.onThemeChange(() => {
      this.updateToggleIcon(toggle);
    });
    
    return toggle;
  }

  static updateToggleIcon(toggle) {
    const effectiveTheme = this.getCurrentEffectiveTheme();
    
    const lightIcon = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
      </svg>
    `;
    
    const darkIcon = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
      </svg>
    `;
    
    toggle.innerHTML = effectiveTheme === this.THEMES.DARK ? lightIcon : darkIcon;
  }
}

// Initialize theme manager when module loads
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    ThemeManager.init();
  });
}
