export default {
  // Common
  common: {
    loading: 'Memuat...',
    save: '<PERSON><PERSON><PERSON>',
    cancel: '<PERSON><PERSON>',
    delete: '<PERSON><PERSON>',
    edit: 'Edit',
    close: '<PERSON>tu<PERSON>',
    back: '<PERSON><PERSON><PERSON>',
    next: 'Selan<PERSON><PERSON><PERSON>',
    previous: '<PERSON><PERSON><PERSON><PERSON>',
    submit: '<PERSON><PERSON>',
    search: '<PERSON><PERSON>',
    filter: 'Filter',
    clear: 'Be<PERSON><PERSON><PERSON>',
    refresh: 'Muat <PERSON>lang',
    retry: '<PERSON><PERSON>',
    confirm: 'Konfirma<PERSON>',
    yes: 'Ya',
    no: 'Tidak',
    ok: 'OK',
    error: 'Error',
    success: 'Berhasil',
    warning: 'Peringatan',
    info: 'Informasi'
  },

  // Navigation
  nav: {
    home: 'Beranda',
    dashboard: 'Dashboard',
    profile: 'Profil',
    settings: 'Pengaturan',
    logout: 'Keluar',
    login: 'Masuk',
    register: 'Daftar'
  },

  // Authentication
  auth: {
    login: {
      title: 'Masuk ke akun Anda',
      subtitle: 'Atau buat akun baru',
      email: 'Alamat email',
      password: 'Kata sandi',
      submit: 'Masuk',
      loading: 'Sedang masuk...',
      success: 'Login berhasil! Mengalihkan...',
      forgotPassword: 'Lupa kata sandi?'
    },
    register: {
      title: 'Buat akun Anda',
      subtitle: 'Atau masuk ke akun yang sudah ada',
      email: 'Alamat email',
      password: 'Kata sandi',
      confirmPassword: 'Konfirmasi Kata Sandi',
      submit: 'Buat Akun',
      loading: 'Membuat Akun...',
      success: 'Akun berhasil dibuat! Mengalihkan...',
      passwordRequirements: 'Kata sandi harus mengandung:',
      requirements: {
        length: 'Minimal 8 karakter',
        letter: 'Minimal satu huruf',
        number: 'Minimal satu angka'
      }
    },
    profile: {
      title: 'Profil Pengguna',
      personalInfo: 'Informasi Pribadi',
      username: 'Nama pengguna',
      fullName: 'Nama Lengkap',
      email: 'Email',
      dateOfBirth: 'Tanggal Lahir',
      gender: 'Jenis Kelamin',
      schoolId: 'ID Sekolah',
      changePassword: 'Ubah Kata Sandi',
      currentPassword: 'Kata Sandi Saat Ini',
      newPassword: 'Kata Sandi Baru',
      deleteAccount: 'Hapus Akun',
      deleteWarning: 'Setelah Anda menghapus akun, tidak ada jalan kembali. Pastikan keputusan Anda.',
      updateSuccess: 'Profil berhasil diperbarui!',
      passwordChangeSuccess: 'Kata sandi berhasil diubah!',
      deleteConfirm: 'Apakah Anda yakin ingin menghapus akun? Tindakan ini tidak dapat dibatalkan dan semua data Anda akan dihapus secara permanen.'
    },
    logout: {
      confirm: 'Apakah Anda yakin ingin keluar?'
    }
  },

  // Dashboard
  dashboard: {
    title: 'Selamat Datang di Dashboard',
    greeting: 'Halo, {{email}}! Ini adalah dashboard Anda.',
    stats: {
      userType: 'Tipe Pengguna',
      tokenBalance: 'Saldo Token',
      accountStatus: 'Status Akun',
      active: 'Aktif',
      inactive: 'Tidak Aktif'
    },
    quickActions: {
      title: 'Aksi Cepat',
      viewProfile: 'Lihat Profil',
      updateSettings: 'Perbarui Pengaturan'
    },
    recentActivity: {
      title: 'Aktivitas Terbaru',
      noActivity: 'Tidak ada aktivitas terbaru untuk ditampilkan'
    }
  },

  // Forms
  forms: {
    validation: {
      required: 'Field ini wajib diisi',
      email: 'Masukkan alamat email yang valid',
      password: 'Kata sandi harus minimal 8 karakter dengan setidaknya satu huruf dan satu angka',
      passwordMismatch: 'Kata sandi tidak cocok',
      username: 'Nama pengguna harus 3-100 karakter dan hanya alfanumerik',
      fullName: 'Nama lengkap maksimal 100 karakter',
      futureDate: 'Tanggal tidak boleh di masa depan',
      invalidGender: 'Jenis kelamin harus pria atau wanita',
      invalidSchoolId: 'ID Sekolah harus berupa bilangan bulat positif'
    }
  },

  // Errors
  errors: {
    network: 'Error jaringan. Periksa koneksi Anda.',
    unauthorized: 'Anda tidak memiliki izin untuk melakukan tindakan ini.',
    server: 'Error server. Silakan coba lagi nanti.',
    notFound: 'Resource yang diminta tidak ditemukan.',
    validation: 'Periksa input Anda dan coba lagi.',
    generic: 'Terjadi error yang tidak terduga.'
  },

  // Success messages
  success: {
    saved: 'Perubahan berhasil disimpan',
    deleted: 'Item berhasil dihapus',
    updated: 'Berhasil diperbarui',
    created: 'Berhasil dibuat'
  },

  // Time and dates
  time: {
    now: 'sekarang',
    today: 'hari ini',
    yesterday: 'kemarin',
    tomorrow: 'besok',
    thisWeek: 'minggu ini',
    lastWeek: 'minggu lalu',
    thisMonth: 'bulan ini',
    lastMonth: 'bulan lalu'
  },

  // Gender options
  gender: {
    male: 'Pria',
    female: 'Wanita',
    other: 'Lainnya',
    preferNotToSay: 'Lebih baik tidak menjawab'
  },

  // PWA
  pwa: {
    installPrompt: 'Instal Aplikasi',
    updateAvailable: 'Versi baru tersedia. Klik untuk memperbarui.',
    offline: 'Anda sedang offline. Beberapa fitur mungkin terbatas.',
    online: 'Koneksi dipulihkan'
  },

  // Accessibility
  a11y: {
    skipToMain: 'Lewati ke konten utama',
    closeDialog: 'Tutup dialog',
    openMenu: 'Buka menu',
    closeMenu: 'Tutup menu',
    loading: 'Memuat konten',
    error: 'Terjadi error'
  }
};
