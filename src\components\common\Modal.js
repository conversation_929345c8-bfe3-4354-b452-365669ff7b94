export class Modal {
  constructor(options = {}) {
    this.options = {
      title: options.title || 'Modal',
      content: options.content || '',
      showCloseButton: options.showCloseButton !== false,
      closeOnBackdrop: options.closeOnBackdrop !== false,
      closeOnEscape: options.closeOnEscape !== false,
      size: options.size || 'md', // sm, md, lg, xl
      ...options
    };
    
    this.modal = null;
    this.backdrop = null;
    this.isOpen = false;
    
    this.create();
    this.attachEventListeners();
  }

  create() {
    // Create backdrop
    this.backdrop = document.createElement('div');
    this.backdrop.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 opacity-0 transition-opacity duration-300';
    
    // Create modal
    this.modal = document.createElement('div');
    this.modal.className = `relative top-20 mx-auto p-5 border w-11/12 shadow-lg rounded-md bg-white transform scale-95 transition-transform duration-300 ${this.getSizeClass()}`;
    
    this.modal.innerHTML = `
      <div class="modal-content">
        ${this.options.showCloseButton ? `
          <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">${this.options.title}</h3>
            <button class="modal-close text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        ` : `
          <div class="pb-3">
            <h3 class="text-lg font-bold text-gray-900">${this.options.title}</h3>
          </div>
        `}
        <div class="modal-body">
          ${this.options.content}
        </div>
      </div>
    `;
    
    this.backdrop.appendChild(this.modal);
  }

  getSizeClass() {
    const sizes = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl'
    };
    return sizes[this.options.size] || sizes.md;
  }

  attachEventListeners() {
    // Close button
    if (this.options.showCloseButton) {
      const closeButton = this.modal.querySelector('.modal-close');
      closeButton.addEventListener('click', () => this.close());
    }

    // Backdrop click
    if (this.options.closeOnBackdrop) {
      this.backdrop.addEventListener('click', (e) => {
        if (e.target === this.backdrop) {
          this.close();
        }
      });
    }

    // Escape key
    if (this.options.closeOnEscape) {
      this.escapeHandler = (e) => {
        if (e.key === 'Escape' && this.isOpen) {
          this.close();
        }
      };
      document.addEventListener('keydown', this.escapeHandler);
    }
  }

  open() {
    if (this.isOpen) return;
    
    document.body.appendChild(this.backdrop);
    document.body.style.overflow = 'hidden';
    
    // Trigger animations
    setTimeout(() => {
      this.backdrop.classList.remove('opacity-0');
      this.modal.classList.remove('scale-95');
      this.modal.classList.add('scale-100');
    }, 10);
    
    this.isOpen = true;
    
    // Focus management
    const firstFocusable = this.modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
      firstFocusable.focus();
    }
    
    // Trigger open callback
    if (this.options.onOpen) {
      this.options.onOpen(this);
    }
  }

  close() {
    if (!this.isOpen) return;
    
    // Trigger animations
    this.backdrop.classList.add('opacity-0');
    this.modal.classList.remove('scale-100');
    this.modal.classList.add('scale-95');
    
    setTimeout(() => {
      if (this.backdrop.parentNode) {
        this.backdrop.parentNode.removeChild(this.backdrop);
      }
      document.body.style.overflow = '';
    }, 300);
    
    this.isOpen = false;
    
    // Trigger close callback
    if (this.options.onClose) {
      this.options.onClose(this);
    }
  }

  destroy() {
    this.close();
    
    // Remove event listeners
    if (this.escapeHandler) {
      document.removeEventListener('keydown', this.escapeHandler);
    }
  }

  setContent(content) {
    const modalBody = this.modal.querySelector('.modal-body');
    if (modalBody) {
      modalBody.innerHTML = content;
    }
  }

  setTitle(title) {
    const titleElement = this.modal.querySelector('h3');
    if (titleElement) {
      titleElement.textContent = title;
    }
  }

  // Static methods for common modal types
  static confirm(options = {}) {
    const modal = new Modal({
      title: options.title || 'Confirm',
      content: `
        <p class="text-gray-700 mb-4">${options.message || 'Are you sure?'}</p>
        <div class="flex justify-end space-x-2">
          <button class="modal-cancel bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded">
            ${options.cancelText || 'Cancel'}
          </button>
          <button class="modal-confirm bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded">
            ${options.confirmText || 'Confirm'}
          </button>
        </div>
      `,
      showCloseButton: false,
      closeOnBackdrop: false,
      closeOnEscape: true
    });

    return new Promise((resolve) => {
      const confirmButton = modal.modal.querySelector('.modal-confirm');
      const cancelButton = modal.modal.querySelector('.modal-cancel');

      confirmButton.addEventListener('click', () => {
        modal.close();
        resolve(true);
      });

      cancelButton.addEventListener('click', () => {
        modal.close();
        resolve(false);
      });

      modal.open();
    });
  }

  static alert(options = {}) {
    const modal = new Modal({
      title: options.title || 'Alert',
      content: `
        <p class="text-gray-700 mb-4">${options.message || 'Alert message'}</p>
        <div class="flex justify-end">
          <button class="modal-ok bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
            ${options.okText || 'OK'}
          </button>
        </div>
      `,
      showCloseButton: false,
      closeOnBackdrop: false,
      closeOnEscape: true
    });

    return new Promise((resolve) => {
      const okButton = modal.modal.querySelector('.modal-ok');

      okButton.addEventListener('click', () => {
        modal.close();
        resolve();
      });

      modal.open();
    });
  }
}
