import { Logger } from '../config/env.js';
import { Toast } from '../components/common/Toast.js';

export class PWAUtils {
  static deferredPrompt = null;
  static isInstalled = false;

  static init() {
    this.checkInstallation();
    this.setupInstallPrompt();
    this.setupServiceWorker();
    this.setupOfflineHandling();
  }

  static checkInstallation() {
    // Check if app is installed
    if (window.matchMedia('(display-mode: standalone)').matches || 
        window.navigator.standalone === true) {
      this.isInstalled = true;
      Logger.info('PWA is installed');
    }
  }

  static setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      
      // Stash the event so it can be triggered later
      this.deferredPrompt = e;
      
      // Show install button
      this.showInstallButton();
      
      Logger.info('PWA install prompt available');
    });

    window.addEventListener('appinstalled', () => {
      this.isInstalled = true;
      this.hideInstallButton();
      Toast.success('App installed successfully!');
      Logger.info('PWA installed');
    });
  }

  static async promptInstall() {
    if (!this.deferredPrompt) {
      Toast.info('Installation is not available on this device');
      return false;
    }

    // Show the install prompt
    this.deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const { outcome } = await this.deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      Logger.info('User accepted the install prompt');
    } else {
      Logger.info('User dismissed the install prompt');
    }

    // Clear the deferredPrompt
    this.deferredPrompt = null;
    
    return outcome === 'accepted';
  }

  static showInstallButton() {
    let installButton = document.getElementById('pwa-install-button');
    
    if (!installButton) {
      installButton = document.createElement('button');
      installButton.id = 'pwa-install-button';
      installButton.className = 'fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
      installButton.innerHTML = `
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <span>Install App</span>
        </div>
      `;
      
      installButton.addEventListener('click', () => {
        this.promptInstall();
      });
      
      document.body.appendChild(installButton);
    }
    
    installButton.style.display = 'block';
  }

  static hideInstallButton() {
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'none';
    }
  }

  static async setupServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available
              this.showUpdateAvailable();
            }
          });
        });
        
        Logger.info('Service Worker registered');
      } catch (error) {
        Logger.error('Service Worker registration failed:', error);
      }
    }
  }

  static showUpdateAvailable() {
    const updateToast = Toast.show(
      'A new version is available. Click to update.',
      'info',
      0 // Don't auto-hide
    );
    
    updateToast.addEventListener('click', () => {
      this.updateApp();
    });
  }

  static async updateApp() {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      
      if (registration && registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          window.location.reload();
        });
      }
    }
  }

  static setupOfflineHandling() {
    window.addEventListener('online', () => {
      Toast.success('Connection restored');
      this.syncOfflineData();
    });

    window.addEventListener('offline', () => {
      Toast.warning('You are now offline. Some features may be limited.');
    });
  }

  static isOnline() {
    return navigator.onLine;
  }

  static async syncOfflineData() {
    // Implement offline data synchronization
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('background-sync');
        Logger.info('Background sync registered');
      } catch (error) {
        Logger.error('Background sync registration failed:', error);
      }
    }
  }

  // Push notifications
  static async requestNotificationPermission() {
    if (!('Notification' in window)) {
      Toast.warning('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }

  static async subscribeToPushNotifications() {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      Toast.warning('Push notifications are not supported');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(process.env.VAPID_PUBLIC_KEY || '')
      });

      Logger.info('Push subscription created');
      return subscription;
    } catch (error) {
      Logger.error('Push subscription failed:', error);
      return null;
    }
  }

  static urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  static showNotification(title, options = {}) {
    if (Notification.permission === 'granted') {
      return new Notification(title, {
        icon: '/icon-192.png',
        badge: '/icon-192.png',
        ...options
      });
    }
  }

  // App shortcuts
  static updateAppShortcuts(shortcuts) {
    if ('navigator' in window && 'setAppBadge' in navigator) {
      // Update app shortcuts if supported
      Logger.info('App shortcuts updated');
    }
  }

  // App badge
  static setAppBadge(count = 0) {
    if ('navigator' in window && 'setAppBadge' in navigator) {
      if (count > 0) {
        navigator.setAppBadge(count);
      } else {
        navigator.clearAppBadge();
      }
    }
  }

  // Share API
  static async share(data) {
    if (navigator.share) {
      try {
        await navigator.share(data);
        return true;
      } catch (error) {
        if (error.name !== 'AbortError') {
          Logger.error('Share failed:', error);
        }
        return false;
      }
    } else {
      // Fallback to clipboard
      if (navigator.clipboard && data.url) {
        try {
          await navigator.clipboard.writeText(data.url);
          Toast.success('Link copied to clipboard');
          return true;
        } catch (error) {
          Logger.error('Clipboard write failed:', error);
          return false;
        }
      }
    }
    return false;
  }

  // File handling
  static async handleFiles(files) {
    if ('launchQueue' in window) {
      // Handle files opened with the app
      window.launchQueue.setConsumer((launchParams) => {
        if (launchParams.files && launchParams.files.length) {
          launchParams.files.forEach(file => {
            Logger.info('File opened:', file.name);
          });
        }
      });
    }
  }

  // Utility methods
  static getInstallationState() {
    return {
      isInstalled: this.isInstalled,
      canInstall: !!this.deferredPrompt,
      isOnline: this.isOnline(),
      hasNotificationPermission: Notification.permission === 'granted'
    };
  }
}

// Auto-initialize PWA features
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    PWAUtils.init();
  });
}
