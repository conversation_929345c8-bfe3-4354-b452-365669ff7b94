import { AuthService } from '../../services/authService.js';
import { Validator, Router } from '../../utils/auth.js';

export class RegisterForm {
  constructor(container) {
    this.container = container;
    this.render();
    this.attachEventListeners();
  }

  render() {
    this.container.innerHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
          <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              Or
              <a href="#" id="switch-to-login" class="font-medium text-blue-600 hover:text-blue-500">
                sign in to your existing account
              </a>
            </p>
          </div>
          <form class="mt-8 space-y-6" id="register-form">
            <div class="rounded-md shadow-sm -space-y-px">
              <div>
                <label for="email" class="sr-only">Email address</label>
                <input 
                  id="email" 
                  name="email" 
                  type="email" 
                  autocomplete="email" 
                  required 
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                  placeholder="Email address"
                >
                <div id="email-error" class="error-message hidden"></div>
              </div>
              <div>
                <label for="password" class="sr-only">Password</label>
                <input 
                  id="password" 
                  name="password" 
                  type="password" 
                  autocomplete="new-password" 
                  required 
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                  placeholder="Password"
                >
                <div id="password-error" class="error-message hidden"></div>
              </div>
              <div>
                <label for="confirmPassword" class="sr-only">Confirm Password</label>
                <input 
                  id="confirmPassword" 
                  name="confirmPassword" 
                  type="password" 
                  autocomplete="new-password" 
                  required 
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                  placeholder="Confirm Password"
                >
                <div id="confirmPassword-error" class="error-message hidden"></div>
              </div>
            </div>

            <div class="text-sm text-gray-600">
              <p>Password must contain:</p>
              <ul class="list-disc list-inside mt-1 space-y-1">
                <li>At least 8 characters</li>
                <li>At least one letter</li>
                <li>At least one number</li>
              </ul>
            </div>

            <div id="form-error" class="error-message hidden text-center"></div>
            <div id="form-success" class="success-message hidden text-center"></div>

            <div>
              <button 
                type="submit" 
                id="register-btn"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span id="register-text">Create Account</span>
                <span id="register-loading" class="hidden">Creating Account...</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    const form = this.container.querySelector('#register-form');
    const switchToLogin = this.container.querySelector('#switch-to-login');

    form.addEventListener('submit', this.handleSubmit.bind(this));
    switchToLogin.addEventListener('click', (e) => {
      e.preventDefault();
      Router.navigate('/login');
    });
  }

  async handleSubmit(e) {
    e.preventDefault();
    
    const email = this.container.querySelector('#email').value;
    const password = this.container.querySelector('#password').value;
    const confirmPassword = this.container.querySelector('#confirmPassword').value;
    
    // Clear previous errors
    this.clearErrors();
    
    // Validate form
    if (!this.validateForm(email, password, confirmPassword)) {
      return;
    }

    // Show loading state
    this.setLoading(true);

    try {
      await AuthService.register(email, password);
      this.showSuccess('Account created successfully! Redirecting...');
      
      setTimeout(() => {
        Router.navigate('/dashboard');
      }, 1000);
    } catch (error) {
      this.showError(error.message);
    } finally {
      this.setLoading(false);
    }
  }

  validateForm(email, password, confirmPassword) {
    let isValid = true;

    if (!Validator.validateRequired(email)) {
      this.showFieldError('email', 'Email is required');
      isValid = false;
    } else if (!Validator.validateEmail(email)) {
      this.showFieldError('email', 'Please enter a valid email address');
      isValid = false;
    }

    if (!Validator.validateRequired(password)) {
      this.showFieldError('password', 'Password is required');
      isValid = false;
    } else if (!Validator.validatePassword(password)) {
      this.showFieldError('password', 'Password must be at least 8 characters with at least one letter and one number');
      isValid = false;
    }

    if (!Validator.validateRequired(confirmPassword)) {
      this.showFieldError('confirmPassword', 'Please confirm your password');
      isValid = false;
    } else if (password !== confirmPassword) {
      this.showFieldError('confirmPassword', 'Passwords do not match');
      isValid = false;
    }

    return isValid;
  }

  showFieldError(fieldName, message) {
    const errorElement = this.container.querySelector(`#${fieldName}-error`);
    errorElement.textContent = message;
    errorElement.classList.remove('hidden');
  }

  showError(message) {
    const errorElement = this.container.querySelector('#form-error');
    errorElement.textContent = message;
    errorElement.classList.remove('hidden');
  }

  showSuccess(message) {
    const successElement = this.container.querySelector('#form-success');
    successElement.textContent = message;
    successElement.classList.remove('hidden');
  }

  clearErrors() {
    const errorElements = this.container.querySelectorAll('.error-message, .success-message');
    errorElements.forEach(element => {
      element.classList.add('hidden');
      element.textContent = '';
    });
  }

  setLoading(loading) {
    const btn = this.container.querySelector('#register-btn');
    const text = this.container.querySelector('#register-text');
    const loadingText = this.container.querySelector('#register-loading');

    btn.disabled = loading;
    
    if (loading) {
      text.classList.add('hidden');
      loadingText.classList.remove('hidden');
    } else {
      text.classList.remove('hidden');
      loadingText.classList.add('hidden');
    }
  }
}
