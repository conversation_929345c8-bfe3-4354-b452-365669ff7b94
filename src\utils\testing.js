export class TestUtils {
  static results = [];
  static currentSuite = null;

  // Test suite management
  static describe(name, fn) {
    const previousSuite = this.currentSuite;
    this.currentSuite = { name, tests: [], passed: 0, failed: 0 };
    
    console.group(`📋 ${name}`);
    
    try {
      fn();
    } catch (error) {
      console.error('Suite setup failed:', error);
    }
    
    console.groupEnd();
    
    this.results.push(this.currentSuite);
    this.currentSuite = previousSuite;
  }

  // Individual test
  static it(description, fn) {
    const test = { description, passed: false, error: null };
    
    try {
      fn();
      test.passed = true;
      this.currentSuite.passed++;
      console.log(`✅ ${description}`);
    } catch (error) {
      test.passed = false;
      test.error = error;
      this.currentSuite.failed++;
      console.error(`❌ ${description}:`, error.message);
    }
    
    this.currentSuite.tests.push(test);
  }

  // Assertions
  static expect(actual) {
    return {
      toBe(expected) {
        if (actual !== expected) {
          throw new Error(`Expected ${actual} to be ${expected}`);
        }
      },
      
      toEqual(expected) {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
        }
      },
      
      toBeTruthy() {
        if (!actual) {
          throw new Error(`Expected ${actual} to be truthy`);
        }
      },
      
      toBeFalsy() {
        if (actual) {
          throw new Error(`Expected ${actual} to be falsy`);
        }
      },
      
      toBeNull() {
        if (actual !== null) {
          throw new Error(`Expected ${actual} to be null`);
        }
      },
      
      toBeUndefined() {
        if (actual !== undefined) {
          throw new Error(`Expected ${actual} to be undefined`);
        }
      },
      
      toContain(expected) {
        if (!actual.includes(expected)) {
          throw new Error(`Expected ${actual} to contain ${expected}`);
        }
      },
      
      toHaveLength(expected) {
        if (actual.length !== expected) {
          throw new Error(`Expected ${actual} to have length ${expected}, got ${actual.length}`);
        }
      },
      
      toThrow(expectedError) {
        try {
          actual();
          throw new Error('Expected function to throw');
        } catch (error) {
          if (expectedError && error.message !== expectedError) {
            throw new Error(`Expected to throw "${expectedError}", got "${error.message}"`);
          }
        }
      },
      
      toBeInstanceOf(expectedClass) {
        if (!(actual instanceof expectedClass)) {
          throw new Error(`Expected ${actual} to be instance of ${expectedClass.name}`);
        }
      },
      
      toBeGreaterThan(expected) {
        if (actual <= expected) {
          throw new Error(`Expected ${actual} to be greater than ${expected}`);
        }
      },
      
      toBeLessThan(expected) {
        if (actual >= expected) {
          throw new Error(`Expected ${actual} to be less than ${expected}`);
        }
      }
    };
  }

  // Mock functions
  static createMock(implementation) {
    const mock = implementation || (() => {});
    mock.calls = [];
    mock.results = [];
    
    const mockFn = (...args) => {
      mock.calls.push(args);
      try {
        const result = mock(...args);
        mock.results.push({ type: 'return', value: result });
        return result;
      } catch (error) {
        mock.results.push({ type: 'throw', value: error });
        throw error;
      }
    };
    
    mockFn.calls = mock.calls;
    mockFn.results = mock.results;
    mockFn.mockReturnValue = (value) => {
      mock.implementation = () => value;
      return mockFn;
    };
    mockFn.mockResolvedValue = (value) => {
      mock.implementation = () => Promise.resolve(value);
      return mockFn;
    };
    mockFn.mockRejectedValue = (error) => {
      mock.implementation = () => Promise.reject(error);
      return mockFn;
    };
    mockFn.mockClear = () => {
      mock.calls.length = 0;
      mock.results.length = 0;
    };
    
    return mockFn;
  }

  // DOM testing utilities
  static createTestElement(html) {
    const container = document.createElement('div');
    container.innerHTML = html;
    return container.firstElementChild;
  }

  static fireEvent(element, eventType, eventInit = {}) {
    const event = new Event(eventType, { bubbles: true, ...eventInit });
    element.dispatchEvent(event);
  }

  static waitFor(condition, timeout = 1000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const check = () => {
        try {
          if (condition()) {
            resolve();
          } else if (Date.now() - startTime > timeout) {
            reject(new Error('Timeout waiting for condition'));
          } else {
            setTimeout(check, 10);
          }
        } catch (error) {
          if (Date.now() - startTime > timeout) {
            reject(error);
          } else {
            setTimeout(check, 10);
          }
        }
      };
      
      check();
    });
  }

  // Async testing
  static async asyncIt(description, fn) {
    const test = { description, passed: false, error: null };
    
    try {
      await fn();
      test.passed = true;
      this.currentSuite.passed++;
      console.log(`✅ ${description}`);
    } catch (error) {
      test.passed = false;
      test.error = error;
      this.currentSuite.failed++;
      console.error(`❌ ${description}:`, error.message);
    }
    
    this.currentSuite.tests.push(test);
  }

  // Setup and teardown
  static beforeEach(fn) {
    this.currentSuite.beforeEach = fn;
  }

  static afterEach(fn) {
    this.currentSuite.afterEach = fn;
  }

  static beforeAll(fn) {
    this.currentSuite.beforeAll = fn;
  }

  static afterAll(fn) {
    this.currentSuite.afterAll = fn;
  }

  // Test reporting
  static getResults() {
    return this.results;
  }

  static printSummary() {
    const totalSuites = this.results.length;
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
    
    console.log('\n📊 Test Summary:');
    console.log(`Suites: ${totalSuites}`);
    console.log(`Tests: ${totalTests}`);
    console.log(`Passed: ${totalPassed}`);
    console.log(`Failed: ${totalFailed}`);
    console.log(`Success Rate: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
  }

  // Performance testing
  static benchmark(name, fn, iterations = 1000) {
    const start = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      fn();
    }
    
    const end = performance.now();
    const total = end - start;
    const average = total / iterations;
    
    console.log(`⏱️ Benchmark "${name}": ${total.toFixed(2)}ms total, ${average.toFixed(4)}ms average`);
    
    return { total, average, iterations };
  }

  // Memory testing
  static measureMemory(fn) {
    if (performance.memory) {
      const before = performance.memory.usedJSHeapSize;
      fn();
      const after = performance.memory.usedJSHeapSize;
      const diff = after - before;
      
      console.log(`🧠 Memory usage: ${diff} bytes`);
      return diff;
    } else {
      console.warn('Memory measurement not available');
      fn();
      return null;
    }
  }

  // Test data generators
  static generateRandomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  static generateRandomEmail() {
    return `${this.generateRandomString(8)}@${this.generateRandomString(5)}.com`;
  }

  static generateRandomNumber(min = 0, max = 100) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // Clear all results
  static clear() {
    this.results = [];
    this.currentSuite = null;
  }
}

// Global test functions
window.describe = TestUtils.describe.bind(TestUtils);
window.it = TestUtils.it.bind(TestUtils);
window.expect = TestUtils.expect.bind(TestUtils);
window.beforeEach = TestUtils.beforeEach.bind(TestUtils);
window.afterEach = TestUtils.afterEach.bind(TestUtils);
window.beforeAll = TestUtils.beforeAll.bind(TestUtils);
window.afterAll = TestUtils.afterAll.bind(TestUtils);
