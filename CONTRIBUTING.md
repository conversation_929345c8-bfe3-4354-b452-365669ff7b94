# Contributing to PetaTalenta Frontend

Thank you for your interest in contributing to PetaTalenta Frontend! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)

## Code of Conduct

This project adheres to a code of conduct. By participating, you are expected to uphold this code. Please report unacceptable behavior to the project maintainers.

### Our Standards

- Use welcoming and inclusive language
- Be respectful of differing viewpoints and experiences
- Gracefully accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

## Getting Started

### Prerequisites

- Node.js 16 or higher
- npm or yarn package manager
- Git
- Basic knowledge of JavaScript, HTML, CSS
- Familiarity with Tailwind CSS

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/your-username/petatalenta-fe.git
   cd petatalenta-fe
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Verify setup**
   - Open http://localhost:5173
   - Ensure the application loads correctly

## Contributing Guidelines

### Types of Contributions

We welcome various types of contributions:

- **Bug fixes**: Fix issues and improve stability
- **Features**: Add new functionality
- **Documentation**: Improve or add documentation
- **Performance**: Optimize code and improve performance
- **Accessibility**: Enhance accessibility features
- **Testing**: Add or improve tests
- **Refactoring**: Improve code structure and maintainability

### Before You Start

1. **Check existing issues**: Look for existing issues or discussions
2. **Create an issue**: For new features or significant changes
3. **Discuss first**: For major changes, discuss with maintainers
4. **Small changes**: Minor fixes can be submitted directly

### Branch Naming

Use descriptive branch names:

- `feature/user-authentication`
- `bugfix/login-validation`
- `docs/api-documentation`
- `refactor/auth-service`

## Pull Request Process

### 1. Prepare Your Changes

```bash
# Create a new branch
git checkout -b feature/your-feature-name

# Make your changes
# ... code changes ...

# Test your changes
npm run test
npm run build

# Commit your changes
git add .
git commit -m "feat: add user authentication feature"
```

### 2. Commit Message Format

Follow conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(auth): add login form validation
fix(router): resolve navigation issue on mobile
docs(readme): update installation instructions
```

### 3. Submit Pull Request

1. **Push your branch**
   ```bash
   git push origin feature/your-feature-name
   ```

2. **Create pull request**
   - Go to GitHub and create a pull request
   - Use the pull request template
   - Provide clear description of changes
   - Link related issues

3. **Pull request checklist**
   - [ ] Code follows project standards
   - [ ] Tests pass
   - [ ] Documentation updated
   - [ ] No breaking changes (or documented)
   - [ ] Accessibility considered
   - [ ] Performance impact assessed

### 4. Review Process

- Maintainers will review your pull request
- Address feedback and requested changes
- Keep discussions constructive and professional
- Be patient during the review process

## Coding Standards

### JavaScript

- Use ES6+ features
- Follow consistent naming conventions
- Use meaningful variable and function names
- Add comments for complex logic
- Avoid global variables

```javascript
// Good
const getUserProfile = async (userId) => {
  try {
    const response = await apiClient.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    throw error;
  }
};

// Bad
const getUser = (id) => {
  return fetch('/api/users/' + id).then(r => r.json());
};
```

### HTML

- Use semantic HTML elements
- Include proper ARIA attributes
- Ensure accessibility compliance
- Use meaningful class names

```html
<!-- Good -->
<button 
  class="btn-primary" 
  aria-label="Submit form"
  type="submit"
>
  Submit
</button>

<!-- Bad -->
<div class="btn" onclick="submit()">Submit</div>
```

### CSS/Tailwind

- Use Tailwind utility classes
- Create custom components for reusable styles
- Follow responsive design principles
- Consider dark mode support

```css
/* Good - Custom component */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}
```

### File Organization

```
src/
├── components/          # Reusable components
│   ├── auth/           # Authentication components
│   ├── common/         # Common UI components
│   └── layout/         # Layout components
├── pages/              # Page components
├── services/           # API services
├── utils/              # Utility functions
├── config/             # Configuration files
└── constants/          # Application constants
```

## Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Writing Tests

- Write tests for new features
- Update tests for modified code
- Use descriptive test names
- Test both success and error cases

```javascript
describe('AuthService', () => {
  it('should login user with valid credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'password123' };
    const result = await AuthService.login(credentials);
    
    expect(result.success).toBe(true);
    expect(result.data.user).toBeDefined();
  });

  it('should throw error with invalid credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'wrong' };
    
    await expect(AuthService.login(credentials)).rejects.toThrow('Login failed');
  });
});
```

## Documentation

### Code Documentation

- Add JSDoc comments for functions and classes
- Document complex algorithms
- Include usage examples

```javascript
/**
 * Validates user input based on specified rules
 * @param {string} input - The input to validate
 * @param {Object} rules - Validation rules
 * @param {boolean} rules.required - Whether input is required
 * @param {RegExp} rules.pattern - Pattern to match
 * @returns {Object} Validation result with isValid and errors
 * @example
 * const result = validateInput('<EMAIL>', { 
 *   required: true, 
 *   pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ 
 * });
 */
function validateInput(input, rules) {
  // Implementation
}
```

### README Updates

- Update README for new features
- Include setup instructions
- Add usage examples
- Update API documentation

## Performance Guidelines

### Best Practices

- Minimize bundle size
- Use lazy loading for non-critical resources
- Optimize images and assets
- Implement proper caching strategies
- Monitor performance metrics

### Code Splitting

```javascript
// Use dynamic imports for code splitting
const LazyComponent = () => import('./LazyComponent.js');
```

## Accessibility Guidelines

- Use semantic HTML
- Provide proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers
- Maintain color contrast ratios

## Security Considerations

- Validate all user inputs
- Sanitize data before display
- Use HTTPS in production
- Implement proper authentication
- Follow OWASP guidelines

## Getting Help

- **Issues**: Create GitHub issues for bugs or questions
- **Discussions**: Use GitHub discussions for general questions
- **Documentation**: Check existing documentation first
- **Community**: Join our community channels

## Recognition

Contributors will be recognized in:
- CHANGELOG.md
- README.md contributors section
- Release notes

Thank you for contributing to PetaTalenta Frontend!
