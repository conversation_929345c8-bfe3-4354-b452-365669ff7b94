import { StringUtils } from '../../utils/helpers.js';

export class Toast {
  static container = null;

  static init() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'toast-container';
      this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
      document.body.appendChild(this.container);
    }
  }

  static show(message, type = 'info', duration = 5000) {
    this.init();

    const toast = this.createToast(message, type);
    this.container.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto remove
    if (duration > 0) {
      setTimeout(() => {
        this.remove(toast);
      }, duration);
    }

    return toast;
  }

  static createToast(message, type) {
    const toast = document.createElement('div');
    const id = StringUtils.generateId();
    toast.id = `toast-${id}`;
    
    const baseClasses = 'transform transition-all duration-300 translate-x-full opacity-0 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden';
    toast.className = baseClasses;

    const { bgColor, textColor, icon } = this.getTypeStyles(type);

    toast.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="${bgColor} rounded-full p-1">
              ${icon}
            </div>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium ${textColor}">
              ${message}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button 
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              onclick="Toast.remove(document.getElementById('toast-${id}'))"
            >
              <span class="sr-only">Close</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;

    return toast;
  }

  static getTypeStyles(type) {
    const styles = {
      success: {
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        icon: `<svg class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>`
      },
      error: {
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        icon: `<svg class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>`
      },
      warning: {
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        icon: `<svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>`
      },
      info: {
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        icon: `<svg class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>`
      }
    };

    return styles[type] || styles.info;
  }

  static remove(toast) {
    if (toast) {
      toast.classList.add('translate-x-full', 'opacity-0');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }
  }

  static success(message, duration = 5000) {
    return this.show(message, 'success', duration);
  }

  static error(message, duration = 7000) {
    return this.show(message, 'error', duration);
  }

  static warning(message, duration = 6000) {
    return this.show(message, 'warning', duration);
  }

  static info(message, duration = 5000) {
    return this.show(message, 'info', duration);
  }

  static clear() {
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}

// Make Toast available globally
window.Toast = Toast;
