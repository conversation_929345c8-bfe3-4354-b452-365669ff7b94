export class FormUtils {
  static getFormData(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (const [key, value] of formData.entries()) {
      data[key] = value;
    }
    
    return data;
  }

  static setFormData(form, data) {
    Object.keys(data).forEach(key => {
      const field = form.querySelector(`[name="${key}"]`);
      if (field) {
        if (field.type === 'checkbox') {
          field.checked = Boolean(data[key]);
        } else if (field.type === 'radio') {
          const radioButton = form.querySelector(`[name="${key}"][value="${data[key]}"]`);
          if (radioButton) {
            radioButton.checked = true;
          }
        } else {
          field.value = data[key] || '';
        }
      }
    });
  }

  static clearForm(form) {
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      if (input.type === 'checkbox' || input.type === 'radio') {
        input.checked = false;
      } else {
        input.value = '';
      }
    });
  }

  static validateForm(form, rules) {
    const errors = {};
    const data = this.getFormData(form);

    Object.keys(rules).forEach(fieldName => {
      const fieldRules = rules[fieldName];
      const value = data[fieldName];

      fieldRules.forEach(rule => {
        if (rule.required && (!value || value.trim() === '')) {
          errors[fieldName] = errors[fieldName] || [];
          errors[fieldName].push(rule.message || `${fieldName} is required`);
        }

        if (value && rule.pattern && !rule.pattern.test(value)) {
          errors[fieldName] = errors[fieldName] || [];
          errors[fieldName].push(rule.message || `${fieldName} is invalid`);
        }

        if (value && rule.minLength && value.length < rule.minLength) {
          errors[fieldName] = errors[fieldName] || [];
          errors[fieldName].push(rule.message || `${fieldName} must be at least ${rule.minLength} characters`);
        }

        if (value && rule.maxLength && value.length > rule.maxLength) {
          errors[fieldName] = errors[fieldName] || [];
          errors[fieldName].push(rule.message || `${fieldName} must be at most ${rule.maxLength} characters`);
        }

        if (rule.custom && typeof rule.custom === 'function') {
          const customError = rule.custom(value, data);
          if (customError) {
            errors[fieldName] = errors[fieldName] || [];
            errors[fieldName].push(customError);
          }
        }
      });
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      data
    };
  }

  static showFieldErrors(form, errors) {
    // Clear previous errors
    this.clearFieldErrors(form);

    Object.keys(errors).forEach(fieldName => {
      const field = form.querySelector(`[name="${fieldName}"]`);
      if (field) {
        // Add error class to field
        field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.remove('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');

        // Show error message
        const errorContainer = form.querySelector(`#${fieldName}-error`) || 
                              form.querySelector(`[data-error-for="${fieldName}"]`);
        
        if (errorContainer) {
          errorContainer.textContent = errors[fieldName][0]; // Show first error
          errorContainer.classList.remove('hidden');
          errorContainer.classList.add('text-red-600', 'text-sm', 'mt-1');
        }
      }
    });
  }

  static clearFieldErrors(form) {
    // Remove error classes from fields
    const fields = form.querySelectorAll('input, select, textarea');
    fields.forEach(field => {
      field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
      field.classList.add('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');
    });

    // Hide error messages
    const errorContainers = form.querySelectorAll('[id$="-error"], [data-error-for]');
    errorContainers.forEach(container => {
      container.textContent = '';
      container.classList.add('hidden');
    });
  }

  static setFieldLoading(field, loading) {
    if (loading) {
      field.disabled = true;
      field.classList.add('opacity-50', 'cursor-not-allowed');
    } else {
      field.disabled = false;
      field.classList.remove('opacity-50', 'cursor-not-allowed');
    }
  }

  static setFormLoading(form, loading) {
    const fields = form.querySelectorAll('input, select, textarea, button');
    fields.forEach(field => {
      this.setFieldLoading(field, loading);
    });
  }

  static addFieldListener(field, event, callback) {
    field.addEventListener(event, callback);
  }

  static debounceValidation(form, rules, delay = 300) {
    let timeout;
    
    const fields = form.querySelectorAll('input, select, textarea');
    fields.forEach(field => {
      field.addEventListener('input', () => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          const validation = this.validateForm(form, rules);
          if (!validation.isValid) {
            this.showFieldErrors(form, validation.errors);
          } else {
            this.clearFieldErrors(form);
          }
        }, delay);
      });
    });
  }

  static serializeForm(form) {
    const data = this.getFormData(form);
    return new URLSearchParams(data).toString();
  }

  static deserializeForm(form, queryString) {
    const params = new URLSearchParams(queryString);
    const data = {};
    
    for (const [key, value] of params) {
      data[key] = value;
    }
    
    this.setFormData(form, data);
  }
}
